# Flights QA System with Gemini API

A intelligent flight and travel assistant powered by Google's Gemini LLM and LangGraph.

## Features

- 🛫 Flight booking and reservation assistance
- ✈️ Flight status and schedule information
- 🌍 Travel planning and recommendations
- 🏢 Airport information and services
- 🧳 Baggage policies and travel regulations
- 💡 Travel tips and advice
- 💬 Conversation history tracking
- ⚙️ Configurable Gemini model parameters

## Setup

### 1. Install Dependencies

```bash
pip install -e .
```

### 2. Get Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your API key
GOOGLE_API_KEY=your-actual-api-key-here
```

### 4. Run Example

```bash
python example_usage.py
```

## Usage

### Basic Usage

```python
import asyncio
from flights_workflow.graph import graph, State

async def ask_question():
    # Configuration
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.7,
            "max_tokens": 1000
        }
    }
    
    # Create state
    state = State(
        query="What documents do I need for international flights?",
        context="Planning a trip to Europe",
        conversation_history=[]
    )
    
    # Get response
    result = await graph.ainvoke(state, config)
    print(result['response'])

asyncio.run(ask_question())
```

### Available Models

- `gemini-2.0-flash`: Fast responses, good for general queries
- `gemini-2.0-pro`: More detailed responses, better for complex questions

### Configuration Options

- `model_name`: Choose between different Gemini models
- `temperature`: Control response creativity (0.0-1.0)
- `max_tokens`: Limit response length

## State Schema

```python
@dataclass
class State:
    query: str = ""                    # User's question
    context: str = ""                  # Additional context
    response: str = ""                 # LLM response
    conversation_history: List = None  # Chat history
```

## Django Integration

This system can be integrated with the Django backend for web-based interactions. The Django app provides REST API endpoints for the flight QA system.

## Development

### Project Structure

```
flights-qa-system/
├── flights_workflow/
│   └── graph.py              # Main LangGraph with Gemini integration
├── flights_qa_system/        # Django project
├── example_usage.py          # Usage examples
├── .env.example             # Environment template
└── README.md               # This file
```

### Adding New Features

1. Extend the `State` class for new data fields
2. Modify `call_gemini_model` function for new logic
3. Update the system prompt for new capabilities
4. Add new nodes to the graph if needed

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure `GOOGLE_API_KEY` is set in your environment
2. **Import Error**: Ensure all dependencies are installed with `pip install -e .`
3. **Rate Limits**: Gemini API has usage limits; check your quota

### Getting Help

- Check the [LangGraph documentation](https://langchain-ai.github.io/langgraph/)
- Review [Gemini API documentation](https://ai.google.dev/docs)
- Open an issue for bugs or feature requests