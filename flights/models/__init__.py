# 导入所有模型，让Django能够发现它们
from .base import (
    model_root,
    who_region,
    climatic_zone,
    data_base2,
    admin_base,
    world_admin,
    unit_admin_amap,
)

from .models import (
    aviation_airports,
    aviation_aircraft,
    aviation_flight,
)

# 确保所有模型都被导出
__all__ = [
    'model_root',
    'who_region',
    'climatic_zone',
    'data_base2',
    'admin_base',
    'world_admin',
    'unit_admin_amap',
    'aviation_airports',
    'aviation_aircraft',
    'aviation_flight',
]