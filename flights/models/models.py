from django.contrib.gis.db.models.fields import Geometry<PERSON>ield
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield, JSONField
from django.db import models

from .base import model_root, world_admin


class aviation_airports(model_root):
    """
    全球机场信息表
    
    存储全球机场的基本信息，包括位置、编码、航班连接等数据。
    支持国内和国际航班的起降统计。
    """
    icao_code = models.CharField(max_length=10, primary_key=True, verbose_name="机场ICAO编号", help_text="国际民航组织机场代码，全球唯一标识符")
    iata_code = models.CharField(max_length=10, null=True, blank=True, verbose_name="机场IATA编号", help_text="国际航空运输协会机场代码，通常用于航班显示")
    country = models.CharField(max_length=255, blank=True, null=True, verbose_name="国家", db_index=True, help_text="机场所在国家名称")
    province = models.CharField(max_length=255, blank=True, null=True, verbose_name="省份")
    province_en = models.CharField(max_length=255, blank=True, null=True, verbose_name="省份英文")
    prefecture = models.CharField(max_length=255, blank=True, null=True, verbose_name="城市")
    prefecture_en = models.CharField(max_length=255, blank=True, null=True, verbose_name="城市英文")
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="机场名称")
    name_en = models.CharField(max_length=255, null=True, blank=True, verbose_name="机场英文")
    timezone_name = models.CharField(max_length=100, blank=True, null=True, verbose_name="机场时区名称")
    timezone_short = models.CharField(max_length=100, blank=True, null=True, verbose_name="机场时区缩写")
    timezone = models.IntegerField(null=True, blank=True, verbose_name="机场时区")
    airport_type = models.CharField(max_length=255, blank=True, null=True, verbose_name="机场类型")
    elevation = models.IntegerField(blank=True, null=True, verbose_name="海拔", help_text="机场的海拔高度，以英尺为单位")
    country_code = models.CharField(max_length=10, null=True, blank=True, verbose_name="国家代码")
    geom_point = GeometryField(null=True, blank=False, spatial_index=False, verbose_name="机场位置点", help_text="机场的地理坐标位置，使用WGS84坐标系")
    to_domestic_airports = ArrayField(models.TextField(), blank=False, null=True, verbose_name="飞往国内机场", help_text="从该机场出发的国内航班目的地机场列表")
    from_domestic_airports = ArrayField(models.TextField(), blank=False, null=True, verbose_name="飞来国内机场", help_text="飞往该机场的国内航班起始机场列表")
    to_international_airports = ArrayField(models.TextField(), blank=False, null=True, verbose_name="飞往国际机场", help_text="从该机场出发的国际航班目的地机场列表")
    from_international_airports = ArrayField(models.TextField(), blank=False, null=True, verbose_name="飞来国际机场", help_text="飞往该机场的国际航班起始机场列表")
    latest_flight_time = models.DateTimeField(null=True, blank=True, verbose_name="最新飞行时间")
    earliest_flight_time = models.DateTimeField(null=True, blank=True, verbose_name="最早飞行时间")
    admin = models.ForeignKey(
        world_admin,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="行政ID",)
    military = models.BooleanField(default=False, verbose_name="军事基地", db_index=True)

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."aviation_airports'
        verbose_name = "全球机场表"
        verbose_name_plural = "全球机场表"
        ordering = ["icao_code"]
        indexes = [
            models.Index(fields=['geom_point'], name='aviation_ai_geom_po_idx'),
            models.Index(fields=['admin'], name='aviation_airports_admin_idx'),
            models.Index(fields=['country'], name='aviation_airports_country_idx'),
            models.Index(fields=['military'], name='aviation_airports_military_idx'),
        ]

    def __str__(self):
        return f"{self.icao_code} - {self.country} {self.name}"


class aviation_aircraft(model_root):
    registration = models.CharField(max_length=20, primary_key=True, verbose_name="飞行器注册号")
    model_name = models.CharField(max_length=255, blank=False, null=True, verbose_name="机型名称", db_index=True)
    model_s = models.CharField(max_length=10, blank=False, null=True, verbose_name="MODE-S")
    serial_number = models.CharField(max_length=50, blank=False, null=True, verbose_name="序列号")
    age = models.IntegerField(blank=False, null=True, verbose_name="机龄")
    born = models.DateTimeField(blank=False, null=True, verbose_name="服役时间")
    track = models.BooleanField(default=False, verbose_name="跟踪轨迹")
    engines = models.CharField(max_length=255, null=True, blank=False, verbose_name="引擎")
    layout = models.CharField(max_length=50, null=True, blank=False, verbose_name="仓位布局")
    total_passenger = models.IntegerField(null=True, blank=False, verbose_name="载客量")
    spotter_source = models.URLField(null=True, blank=False, verbose_name="spotter链接")
    as_cargo = models.BooleanField(default=False, verbose_name="货机", db_index=True)
    as_cargo_date = models.DateTimeField(blank=False, null=True, verbose_name="疑似货机时间")
    military = models.BooleanField(default=False, verbose_name="军机", db_index=True)
    manufacturer = models.CharField(max_length=100, null=True, blank=False, db_index=True, verbose_name="制造商")
    airline_name = models.CharField(max_length=255,
        null=True,
        blank=False,
        verbose_name="航空公司名称",
        db_index=True,)

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."aviation_aircraft'
        verbose_name = "全球飞机表"
        verbose_name_plural = "全球飞机表"
        ordering = ["registration"]
        indexes = [
            models.Index(fields=['airline_name'], name='aviation_aircraft_airline_idx'),
            models.Index(fields=['as_cargo'], name='aviation_aircraft_cargo_idx'),
            models.Index(fields=['manufacturer'], name='aviation_aircraft_manufacturer_idx'),
            models.Index(fields=['military'], name='aviation_aircraft_military_idx'),
            models.Index(fields=['model_name'], name='aviation_aircraft_model_idx'),
        ]


class aviation_flight(model_root):
    """
    全球航班信息表
    
    记录全球航班的详细信息，包括航班号、航空器、起降机场、时间安排等。
    支持航班状态跟踪和国际/国内航班分类。
    """
    flight_id = models.PositiveBigIntegerField(primary_key=True, verbose_name="Radarbox飞行ID", db_index=True, help_text="Radarbox系统中的唯一航班标识符")
    aircraft = models.ForeignKey(
        aviation_aircraft,
        db_index=True,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
        verbose_name="航空器",
        help_text="执行该航班的飞机信息")
    flight_number_icao = models.CharField(max_length=10, null=True, blank=False, verbose_name="航班编号（ICAO）", help_text="国际民航组织标准的航班号")
    flight_number_iata = models.CharField(max_length=10, null=True, blank=False, verbose_name="航班编号（IATA）", help_text="国际航空运输协会标准的航班号，通常用于乘客显示")
    code_shares = ArrayField(models.TextField(), null=True, blank=False, verbose_name="共享航班号")
    airline_iata = models.CharField(max_length=5, null=True, blank=False, verbose_name="航空公司代码（IATA）")
    airline_icao = models.CharField(max_length=5, null=True, blank=False, verbose_name="航空公司代码（ICAO）")
    airline_name = models.CharField(max_length=255, null=True, blank=False, verbose_name="航空公司名称")
    from_airport = models.ForeignKey(
        aviation_airports,
        db_index=True,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
        verbose_name="出发机场",
        related_name="from_aviation_airports_from",)
    to_airport = models.ForeignKey(
        aviation_airports,
        db_index=True,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
        verbose_name="到达机场",
        related_name="aviation_airports_to",)
    status = models.CharField(max_length=100, null=True, blank=False, verbose_name="航班状态", db_index=True)
    departure_scheduled_time = models.DateTimeField(blank=False, null=True, verbose_name="计划起飞时间")
    departure_actual_time = models.DateTimeField(blank=False, null=True, verbose_name="起飞时间")
    arrival_scheduled_time = models.DateTimeField(blank=False, null=True, verbose_name="计划到达时间")
    arrival_actual_time = models.DateTimeField(blank=False, null=True, verbose_name="到达时间")
    arrdeld = models.CharField(max_length=100, blank=False, null=True, verbose_name="到达ID")
    tkosrc = models.CharField(max_length=100, blank=False, null=True)
    deptaxi = models.CharField(max_length=100, blank=False, null=True)
    depgate = models.CharField(max_length=50, blank=False, null=True, verbose_name="登机口")
    arrgate = models.CharField(max_length=50, blank=False, null=True, verbose_name="到达口")
    as_international = models.BooleanField(null=True, blank=False, verbose_name="是否为国际航班", db_index=True)

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."aviation_flight'
        verbose_name = "全球航班表"
        verbose_name_plural = "全球航班表"
        ordering = ["-departure_scheduled_time"]
        indexes = [
            models.Index(fields=['aircraft'], name='aviation_flight_aircraft_idx'),
            models.Index(fields=['airline_name'], name='aviation_flight_airline_name_idx'),
            models.Index(fields=['departure_scheduled_time'], name='aviation_flight_departure_idx'),
            models.Index(fields=['as_international'], name='aviation_flight_international_idx'),
            models.Index(fields=['flight_id'], name='aviation_flight_id_idx'),
            models.Index(fields=['from_airport'], name='aviation_flight_from_airport_idx'),
            models.Index(fields=['to_airport'], name='aviation_flight_to_airport_idx'),
            models.Index(fields=['status'], name='aviation_flight_status_idx'),
        ]
