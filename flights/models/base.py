from django.contrib.gis.db.models.fields import GeometryField
from django.db import models

models.options.DEFAULT_NAMES += ("db_schema",)


class model_root(models.Model):
    """基础模型，包含创建和更新时间"""

    sys_created_at = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    sys_updated_at = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    class Meta:
        abstract = True


class who_region(model_root):
    region_code = models.CharField(max_length=5, primary_key=True, verbose_name="区域代码")
    name_cn = models.CharField(max_length=255, null=True, blank=False, verbose_name="中文名称")
    name_en = models.CharField(max_length=255, null=True, blank=False, verbose_name="英文名称")

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."unit_who_region'
        verbose_name = "WHO区域"
        verbose_name_plural = "WHO区域"

    def __str__(self):
        return f"{self.name_cn} ({self.region_code})"


class climatic_zone(model_root):
    type_en = models.CharField(max_length=20, null=True, blank=False, verbose_name="气候带英文名")
    type_cn = models.CharField(max_length=20, null=True, blank=False, verbose_name="气候带中文名")
    type_cn_sphere = models.CharField(max_length=20, null=True, blank=False, verbose_name="气候带半球中文名")
    type_sub_en = models.CharField(max_length=20, null=True, blank=False, verbose_name="子气候带英文名")
    type_sub_cn = models.CharField(max_length=20, null=True, blank=False, verbose_name="子气候带中文名")
    description = models.CharField(max_length=255, null=True, blank=False, verbose_name="描述")
    geom_polygon = GeometryField(srid=4326, spatial_index=False, verbose_name="气候带图形")

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."unit_climatic_zone'
        verbose_name = "气候带"
        verbose_name_plural = "气候带"

    def __str__(self):
        return f"{self.type_cn} - {self.type_sub_cn}"


class data_base2(model_root):
    base_id = models.UUIDField(primary_key=True, verbose_name="数据ID")
    name = models.CharField(max_length=255, blank=False, null=True, verbose_name="名称")
    source_url = models.URLField(blank=False, null=True, verbose_name="数据链接")
    available = models.BooleanField(default=True, verbose_name="数据可用")
    exists = models.BooleanField(default=True, blank=False, null=True, verbose_name="数据存在")
    open_date = models.DateField(blank=False, null=True, verbose_name="成立时间")
    close_date = models.DateField(blank=False, null=True, verbose_name="关闭时间")

    class Meta:
        abstract = True
        db_schema = "ingredient"


class admin_base(data_base2):
    cn_name = models.CharField(max_length=255, blank=False, null=True, verbose_name="中文名称")
    cn_name_abbr = models.CharField(max_length=255, blank=False, null=True, verbose_name="中文简称")
    level = models.CharField(max_length=255, blank=False, null=True, verbose_name="行政级别", db_index=True)
    parent_id = models.UUIDField(blank=False, null=True, verbose_name="数据ID", db_index=True)
    geom_point = GeometryField(srid=4326, blank=False, null=True, verbose_name="位置点", spatial_index=False)
    geom_polygon = GeometryField(srid=4326, blank=False, null=True, verbose_name="位置面", spatial_index=False)

    class Meta(data_base2.Meta):
        abstract = True


class world_admin(admin_base):
    iso_code1 = models.CharField(max_length=10, null=True, blank=False, verbose_name="标准编码1", db_index=True)
    iso_code2 = models.CharField(max_length=10, null=True, blank=False, verbose_name="标准编码2", db_index=True)
    iso_code2_num = models.SmallIntegerField(null=True, blank=False, verbose_name="标准编码3数字", db_index=True)
    local_admin_code = models.CharField(max_length=255, null=True, blank=False, verbose_name="当地编码", db_index=True)
    en_name = models.CharField(max_length=200, null=True, blank=False, verbose_name="英文名称", db_index=True)
    en_name_abbr = models.CharField(max_length=200,
        null=True,
        blank=False,
        verbose_name="英文名称简称",
        db_index=True,)
    roman_name = models.CharField(max_length=200, null=True, blank=False, verbose_name="英文名称", db_index=True)
    local_name = models.CharField(max_length=200, null=True, blank=False, verbose_name="本地名称", db_index=True)
    local_name_abbr = models.CharField(max_length=200,
        null=True,
        blank=False,
        verbose_name="本地名称简称",
        db_index=True,)
    city_code = models.CharField(max_length=10, null=True, db_index=True, blank=False, verbose_name="城市区号")
    climatic_zone = models.ForeignKey(
        climatic_zone,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
        verbose_name="气候带ID",)
    climatic_total = models.PositiveIntegerField(blank=False, null=True, verbose_name="横跨气候带数量")
    geom_polygon_sim = GeometryField(srid=4326,
        blank=False,
        null=True,
        verbose_name="简化位置面",
        spatial_index=False,)
    source = models.CharField(max_length=255, null=True, blank=False, verbose_name="数据源")
    version = models.DateField(null=True, blank=False, verbose_name="数据版本")
    description = models.TextField(null=True, blank=False, verbose_name="说明")
    independent = models.BooleanField(default=True, verbose_name="独立主权")
    domain = models.CharField(max_length=10, null=True, blank=False, verbose_name="域名")
    region = models.CharField(max_length=10, null=True, blank=False, verbose_name="宏观地理区域")
    sub_region = models.CharField(max_length=50, null=True, blank=False, verbose_name="地理亚区域")
    region_en = models.CharField(max_length=10, null=True, blank=False, verbose_name="宏观地理区域英文")
    sub_region_en = models.CharField(max_length=50, null=True, blank=False, verbose_name="地理亚区域英文")
    develop_region = models.CharField(max_length=50, null=True, blank=False, verbose_name="千年发展分区")
    undeveloped = models.BooleanField(default=False, verbose_name="最不发达国家")
    developing = models.BooleanField(default=False, verbose_name="内陆发展中国家")
    island_developing = models.BooleanField(default=False, verbose_name="小岛屿发展中国家")
    world_bank_income_class = models.CharField(max_length=10, null=True, blank=False, verbose_name="世界银行收入分组")
    who_region = models.ForeignKey(
        who_region,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
        verbose_name="WHO分区",)
    hemisphere = models.CharField(max_length=10, null=True, blank=False, verbose_name="半球")
    influenza_transmission_zone = models.CharField(max_length=20, null=True, blank=False, verbose_name="流感传播区")

    class Meta:
        db_schema = "flavor"
        db_table = f'{db_schema}"."unit_world_admin'
        verbose_name = "世界行政区划"
        verbose_name_plural = "世界行政区划"

    def __str__(self):
        return f"{self.name} ({self.level})"


class unit_admin_amap(admin_base):
    admin_code = models.CharField(max_length=10, null=True, blank=False, verbose_name="行政编号", db_index=True)
    city_code = models.CharField(max_length=10, null=True, db_index=True, blank=False, verbose_name="城市区号")
    geom_polygon_sim = GeometryField(srid=4326,
        blank=False,
        null=True,
        verbose_name="简化区划面",
        spatial_index=False,)

    class Meta:
        db_schema = "ingredient"
        db_table = f'{db_schema}"."unit_admin_amap'
        verbose_name = "中国各级行政区划"
        verbose_name_plural = "中国各级行政区划"

    def __str__(self):
        return f"{self.name} ({self.level})"
