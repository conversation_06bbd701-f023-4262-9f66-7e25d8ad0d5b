# Generated by Django 5.2.2 on 2025-08-11 09:39

import django.contrib.gis.db.models.fields
import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="aviation_aircraft",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "registration",
                    models.CharField(
                        max_length=20,
                        primary_key=True,
                        serialize=False,
                        verbose_name="飞行器注册号",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="机型名称",
                    ),
                ),
                (
                    "model_s",
                    models.CharField(max_length=10, null=True, verbose_name="MODE-S"),
                ),
                (
                    "serial_number",
                    models.CharField(max_length=50, null=True, verbose_name="序列号"),
                ),
                ("age", models.IntegerField(null=True, verbose_name="机龄")),
                ("born", models.DateTimeField(null=True, verbose_name="服役时间")),
                ("track", models.BooleanField(default=False, verbose_name="跟踪轨迹")),
                (
                    "engines",
                    models.CharField(max_length=255, null=True, verbose_name="引擎"),
                ),
                (
                    "layout",
                    models.CharField(max_length=50, null=True, verbose_name="仓位布局"),
                ),
                (
                    "total_passenger",
                    models.IntegerField(null=True, verbose_name="载客量"),
                ),
                (
                    "spotter_source",
                    models.URLField(null=True, verbose_name="spotter链接"),
                ),
                (
                    "as_cargo",
                    models.BooleanField(
                        db_index=True, default=False, verbose_name="货机"
                    ),
                ),
                (
                    "as_cargo_date",
                    models.DateTimeField(null=True, verbose_name="疑似货机时间"),
                ),
                (
                    "military",
                    models.BooleanField(
                        db_index=True, default=False, verbose_name="军机"
                    ),
                ),
                (
                    "manufacturer",
                    models.CharField(
                        db_index=True, max_length=100, null=True, verbose_name="制造商"
                    ),
                ),
                (
                    "airline_name",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="航空公司名称",
                    ),
                ),
            ],
            options={
                "verbose_name": "全球飞机表",
                "verbose_name_plural": "全球飞机表",
                "db_table": 'flavor"."aviation_aircraft',
                "ordering": ["registration"],
            },
        ),
        migrations.CreateModel(
            name="aviation_airports",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "icao_code",
                    models.CharField(
                        max_length=10,
                        primary_key=True,
                        serialize=False,
                        verbose_name="机场ICAO编号",
                    ),
                ),
                (
                    "iata_code",
                    models.CharField(
                        blank=True,
                        max_length=10,
                        null=True,
                        verbose_name="机场IATA编号",
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="国家",
                    ),
                ),
                (
                    "province",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="省份"
                    ),
                ),
                (
                    "province_en",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="省份英文"
                    ),
                ),
                (
                    "prefecture",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="城市"
                    ),
                ),
                (
                    "prefecture_en",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="城市英文"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="机场名称"
                    ),
                ),
                (
                    "name_en",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="机场英文"
                    ),
                ),
                (
                    "timezone_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="机场时区名称",
                    ),
                ),
                (
                    "timezone_short",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="机场时区缩写",
                    ),
                ),
                (
                    "timezone",
                    models.IntegerField(blank=True, null=True, verbose_name="机场时区"),
                ),
                (
                    "airport_type",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="机场类型"
                    ),
                ),
                (
                    "elevation",
                    models.IntegerField(blank=True, null=True, verbose_name="海拔"),
                ),
                (
                    "country_code",
                    models.CharField(
                        blank=True, max_length=10, null=True, verbose_name="国家代码"
                    ),
                ),
                (
                    "geom_point",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True,
                        spatial_index=False,
                        srid=4326,
                        verbose_name="机场位置点",
                    ),
                ),
                (
                    "to_domestic_airports",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(),
                        null=True,
                        size=None,
                        verbose_name="飞往国内机场",
                    ),
                ),
                (
                    "from_domestic_airports",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(),
                        null=True,
                        size=None,
                        verbose_name="飞来国内机场",
                    ),
                ),
                (
                    "to_international_airports",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(),
                        null=True,
                        size=None,
                        verbose_name="飞往国际机场",
                    ),
                ),
                (
                    "from_international_airports",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(),
                        null=True,
                        size=None,
                        verbose_name="飞来国际机场",
                    ),
                ),
                (
                    "latest_flight_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最新飞行时间"
                    ),
                ),
                (
                    "earliest_flight_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最早飞行时间"
                    ),
                ),
                (
                    "military",
                    models.BooleanField(
                        db_index=True, default=False, verbose_name="军事基地"
                    ),
                ),
            ],
            options={
                "verbose_name": "全球机场表",
                "verbose_name_plural": "全球机场表",
                "db_table": 'flavor"."aviation_airports',
                "ordering": ["icao_code"],
            },
        ),
        migrations.CreateModel(
            name="climatic_zone",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "type_en",
                    models.CharField(
                        max_length=20, null=True, verbose_name="气候带英文名"
                    ),
                ),
                (
                    "type_cn",
                    models.CharField(
                        max_length=20, null=True, verbose_name="气候带中文名"
                    ),
                ),
                (
                    "type_cn_sphere",
                    models.CharField(
                        max_length=20, null=True, verbose_name="气候带半球中文名"
                    ),
                ),
                (
                    "type_sub_en",
                    models.CharField(
                        max_length=20, null=True, verbose_name="子气候带英文名"
                    ),
                ),
                (
                    "type_sub_cn",
                    models.CharField(
                        max_length=20, null=True, verbose_name="子气候带中文名"
                    ),
                ),
                (
                    "description",
                    models.CharField(max_length=255, null=True, verbose_name="描述"),
                ),
                (
                    "geom_polygon",
                    django.contrib.gis.db.models.fields.GeometryField(
                        spatial_index=False, srid=4326, verbose_name="气候带图形"
                    ),
                ),
            ],
            options={
                "verbose_name": "气候带",
                "verbose_name_plural": "气候带",
                "db_table": 'flavor"."unit_climatic_zone',
            },
        ),
        migrations.CreateModel(
            name="unit_admin_amap",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "base_id",
                    models.UUIDField(
                        primary_key=True, serialize=False, verbose_name="数据ID"
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, null=True, verbose_name="名称"),
                ),
                ("source_url", models.URLField(null=True, verbose_name="数据链接")),
                (
                    "available",
                    models.BooleanField(default=True, verbose_name="数据可用"),
                ),
                (
                    "exists",
                    models.BooleanField(
                        default=True, null=True, verbose_name="数据存在"
                    ),
                ),
                ("open_date", models.DateField(null=True, verbose_name="成立时间")),
                ("close_date", models.DateField(null=True, verbose_name="关闭时间")),
                (
                    "cn_name",
                    models.CharField(
                        max_length=255, null=True, verbose_name="中文名称"
                    ),
                ),
                (
                    "cn_name_abbr",
                    models.CharField(
                        max_length=255, null=True, verbose_name="中文简称"
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="行政级别",
                    ),
                ),
                (
                    "parent_id",
                    models.UUIDField(db_index=True, null=True, verbose_name="数据ID"),
                ),
                (
                    "geom_point",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True, spatial_index=False, srid=4326, verbose_name="位置点"
                    ),
                ),
                (
                    "geom_polygon",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True, spatial_index=False, srid=4326, verbose_name="位置面"
                    ),
                ),
                (
                    "admin_code",
                    models.CharField(
                        db_index=True, max_length=10, null=True, verbose_name="行政编号"
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        db_index=True, max_length=10, null=True, verbose_name="城市区号"
                    ),
                ),
                (
                    "geom_polygon_sim",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True,
                        spatial_index=False,
                        srid=4326,
                        verbose_name="简化区划面",
                    ),
                ),
            ],
            options={
                "verbose_name": "中国各级行政区划",
                "verbose_name_plural": "中国各级行政区划",
                "db_table": 'ingredient"."unit_admin_amap',
            },
        ),
        migrations.CreateModel(
            name="who_region",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "region_code",
                    models.CharField(
                        max_length=5,
                        primary_key=True,
                        serialize=False,
                        verbose_name="区域代码",
                    ),
                ),
                (
                    "name_cn",
                    models.CharField(
                        max_length=255, null=True, verbose_name="中文名称"
                    ),
                ),
                (
                    "name_en",
                    models.CharField(
                        max_length=255, null=True, verbose_name="英文名称"
                    ),
                ),
            ],
            options={
                "verbose_name": "WHO区域",
                "verbose_name_plural": "WHO区域",
                "db_table": 'flavor"."unit_who_region',
            },
        ),
        migrations.CreateModel(
            name="aviation_flight",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "flight_id",
                    models.PositiveBigIntegerField(
                        db_index=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="Radarbox飞行ID",
                    ),
                ),
                (
                    "flight_number_icao",
                    models.CharField(
                        max_length=10, null=True, verbose_name="航班编号（ICAO）"
                    ),
                ),
                (
                    "flight_number_iata",
                    models.CharField(
                        max_length=10, null=True, verbose_name="航班编号（IATA）"
                    ),
                ),
                (
                    "code_shares",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(),
                        null=True,
                        size=None,
                        verbose_name="共享航班号",
                    ),
                ),
                (
                    "airline_iata",
                    models.CharField(
                        max_length=5, null=True, verbose_name="航空公司代码（IATA）"
                    ),
                ),
                (
                    "airline_icao",
                    models.CharField(
                        max_length=5, null=True, verbose_name="航空公司代码（ICAO）"
                    ),
                ),
                (
                    "airline_name",
                    models.CharField(
                        max_length=255, null=True, verbose_name="航空公司名称"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        null=True,
                        verbose_name="航班状态",
                    ),
                ),
                (
                    "departure_scheduled_time",
                    models.DateTimeField(null=True, verbose_name="计划起飞时间"),
                ),
                (
                    "departure_actual_time",
                    models.DateTimeField(null=True, verbose_name="起飞时间"),
                ),
                (
                    "arrival_scheduled_time",
                    models.DateTimeField(null=True, verbose_name="计划到达时间"),
                ),
                (
                    "arrival_actual_time",
                    models.DateTimeField(null=True, verbose_name="到达时间"),
                ),
                (
                    "arrdeld",
                    models.CharField(max_length=100, null=True, verbose_name="到达ID"),
                ),
                ("tkosrc", models.CharField(max_length=100, null=True)),
                ("deptaxi", models.CharField(max_length=100, null=True)),
                (
                    "depgate",
                    models.CharField(max_length=50, null=True, verbose_name="登机口"),
                ),
                (
                    "arrgate",
                    models.CharField(max_length=50, null=True, verbose_name="到达口"),
                ),
                (
                    "as_international",
                    models.BooleanField(
                        db_index=True, null=True, verbose_name="是否为国际航班"
                    ),
                ),
                (
                    "aircraft",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="flights.aviation_aircraft",
                        verbose_name="航空器",
                    ),
                ),
                (
                    "from_airport",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="from_aviation_airports_from",
                        to="flights.aviation_airports",
                        verbose_name="出发机场",
                    ),
                ),
                (
                    "to_airport",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="aviation_airports_to",
                        to="flights.aviation_airports",
                        verbose_name="到达机场",
                    ),
                ),
            ],
            options={
                "verbose_name": "全球航班表",
                "verbose_name_plural": "全球航班表",
                "db_table": 'flavor"."aviation_flight',
                "ordering": ["-departure_scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="world_admin",
            fields=[
                (
                    "sys_created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "sys_updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "base_id",
                    models.UUIDField(
                        primary_key=True, serialize=False, verbose_name="数据ID"
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, null=True, verbose_name="名称"),
                ),
                ("source_url", models.URLField(null=True, verbose_name="数据链接")),
                (
                    "available",
                    models.BooleanField(default=True, verbose_name="数据可用"),
                ),
                (
                    "exists",
                    models.BooleanField(
                        default=True, null=True, verbose_name="数据存在"
                    ),
                ),
                ("open_date", models.DateField(null=True, verbose_name="成立时间")),
                ("close_date", models.DateField(null=True, verbose_name="关闭时间")),
                (
                    "cn_name",
                    models.CharField(
                        max_length=255, null=True, verbose_name="中文名称"
                    ),
                ),
                (
                    "cn_name_abbr",
                    models.CharField(
                        max_length=255, null=True, verbose_name="中文简称"
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="行政级别",
                    ),
                ),
                (
                    "parent_id",
                    models.UUIDField(db_index=True, null=True, verbose_name="数据ID"),
                ),
                (
                    "geom_point",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True, spatial_index=False, srid=4326, verbose_name="位置点"
                    ),
                ),
                (
                    "geom_polygon",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True, spatial_index=False, srid=4326, verbose_name="位置面"
                    ),
                ),
                (
                    "iso_code1",
                    models.CharField(
                        db_index=True,
                        max_length=10,
                        null=True,
                        verbose_name="标准编码1",
                    ),
                ),
                (
                    "iso_code2",
                    models.CharField(
                        db_index=True,
                        max_length=10,
                        null=True,
                        verbose_name="标准编码2",
                    ),
                ),
                (
                    "iso_code2_num",
                    models.SmallIntegerField(
                        db_index=True, null=True, verbose_name="标准编码3数字"
                    ),
                ),
                (
                    "local_admin_code",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="当地编码",
                    ),
                ),
                (
                    "en_name",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        null=True,
                        verbose_name="英文名称",
                    ),
                ),
                (
                    "en_name_abbr",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        null=True,
                        verbose_name="英文名称简称",
                    ),
                ),
                (
                    "roman_name",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        null=True,
                        verbose_name="英文名称",
                    ),
                ),
                (
                    "local_name",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        null=True,
                        verbose_name="本地名称",
                    ),
                ),
                (
                    "local_name_abbr",
                    models.CharField(
                        db_index=True,
                        max_length=200,
                        null=True,
                        verbose_name="本地名称简称",
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        db_index=True, max_length=10, null=True, verbose_name="城市区号"
                    ),
                ),
                (
                    "climatic_total",
                    models.PositiveIntegerField(
                        null=True, verbose_name="横跨气候带数量"
                    ),
                ),
                (
                    "geom_polygon_sim",
                    django.contrib.gis.db.models.fields.GeometryField(
                        null=True,
                        spatial_index=False,
                        srid=4326,
                        verbose_name="简化位置面",
                    ),
                ),
                (
                    "source",
                    models.CharField(max_length=255, null=True, verbose_name="数据源"),
                ),
                ("version", models.DateField(null=True, verbose_name="数据版本")),
                ("description", models.TextField(null=True, verbose_name="说明")),
                (
                    "independent",
                    models.BooleanField(default=True, verbose_name="独立主权"),
                ),
                (
                    "domain",
                    models.CharField(max_length=10, null=True, verbose_name="域名"),
                ),
                (
                    "region",
                    models.CharField(
                        max_length=10, null=True, verbose_name="宏观地理区域"
                    ),
                ),
                (
                    "sub_region",
                    models.CharField(
                        max_length=50, null=True, verbose_name="地理亚区域"
                    ),
                ),
                (
                    "region_en",
                    models.CharField(
                        max_length=10, null=True, verbose_name="宏观地理区域英文"
                    ),
                ),
                (
                    "sub_region_en",
                    models.CharField(
                        max_length=50, null=True, verbose_name="地理亚区域英文"
                    ),
                ),
                (
                    "develop_region",
                    models.CharField(
                        max_length=50, null=True, verbose_name="千年发展分区"
                    ),
                ),
                (
                    "undeveloped",
                    models.BooleanField(default=False, verbose_name="最不发达国家"),
                ),
                (
                    "developing",
                    models.BooleanField(default=False, verbose_name="内陆发展中国家"),
                ),
                (
                    "island_developing",
                    models.BooleanField(default=False, verbose_name="小岛屿发展中国家"),
                ),
                (
                    "world_bank_income_class",
                    models.CharField(
                        max_length=10, null=True, verbose_name="世界银行收入分组"
                    ),
                ),
                (
                    "hemisphere",
                    models.CharField(max_length=10, null=True, verbose_name="半球"),
                ),
                (
                    "influenza_transmission_zone",
                    models.CharField(
                        max_length=20, null=True, verbose_name="流感传播区"
                    ),
                ),
                (
                    "climatic_zone",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="flights.climatic_zone",
                        verbose_name="气候带ID",
                    ),
                ),
                (
                    "who_region",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="flights.who_region",
                        verbose_name="WHO分区",
                    ),
                ),
            ],
            options={
                "verbose_name": "世界行政区划",
                "verbose_name_plural": "世界行政区划",
                "db_table": 'flavor"."unit_world_admin',
            },
        ),
        migrations.AddField(
            model_name="aviation_airports",
            name="admin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="flights.world_admin",
                verbose_name="行政ID",
            ),
        ),
    ]
