航班查询系统Queryset生成准确率优化项目规划文档

  📋 项目概述

  项目目标

  将航班查询系统的queryset生成准确率从30%提升至70%+，通过重构验证流程和增强语义验证机制来实现。

  背景说明

  当前系统使用ReAct架构，通过LLM将用户自然语言查询转换为符合FLIGHT_QUERY_SCHEMA格式的查询，但存在以下问题：
  - 生成的查询Schema与实际数据库模型字段不匹配
  - 验证机制不够完善，无法捕获语义层面的错误
  - 工具链路过于分散，错误处理不统一

  🔍 当前系统分析

  架构现状

  用户查询 → generate_flight_query → validate_flight_query_schema → execute_flight_query → 结果返回

  关键文件结构

  flights_workflow/
  ├── graph.py                          # ReAct工作流主文件
  ├── tools/
  │   ├── generate_query_schema_tool.py  # Schema生成工具
  │   ├── flight_query_schema.py         # Schema定义和验证
  │   ├── query_parser.py                # 查询解析器
  │   ├── introspect_models.py           # 模型内省工具
  │   └── sample_data.py                 # 样例数据工具

  问题诊断

  1. 硬编码字段问题

  - 位置: flight_query_schema.py:58-156
  - 问题: MODEL_FIELDS使用硬编码定义，与实际Django模型不同步
  - 影响: 生成的Schema包含不存在的字段，导致查询失败

  2. 验证机制缺陷

  - 位置: flight_query_schema.py:352-708
  - 问题: 只验证Schema格式，不验证语义合理性
  - 影响: 格式正确但逻辑错误的查询通过验证

  3. 工具链分散

  - 位置: graph.py:248-268
  - 问题: 验证和执行分离，LLM需要多次决策
  - 影响: 增加错误概率，降低用户体验

  🚀 技术改进方案

  核心改进策略

  1. 工具重构：验证与执行合并

  - 将验证和执行合并为一个原子操作
  - 减少LLM决策次数：3步 → 2步
  - 统一错误处理和返回格式

  2. 双重验证机制

  - 语法验证：本地快速验证（字段存在性、Schema格式）
  - 语义验证：LLM验证（查询逻辑与用户意图匹配性）

  3. 动态字段获取

  - 使用introspect_models实时获取模型字段
  - 替代硬编码的MODEL_FIELDS定义

  📝 详细开发任务
Phase 1: 创建核心验证执行工具 (优先级: 高)

  任务 1.1: 创建comprehensive_query_tool.py

  估时: 2天负责人: 开发者A

  详细要求:
  - 创建新文件: flights_workflow/tools/comprehensive_query_tool.py
  - 实现comprehensive_flight_query工具函数
  - 集成语法验证、语义验证、查询执行三个步骤
  - 提供统一的错误处理和返回格式

  关键功能:
  @tool
  def comprehensive_flight_query(query_schema: str, user_question: str) -> str:
      """完整的航班查询：语法验证 → 语义验证 → 执行"""
      pass

  def validate_syntax_locally(query_schema: str) -> Dict[str, Any]:
      """本地语法验证"""
      pass

  def validate_semantics_with_llm(query_schema: str, user_question: str, generated_sql: str) -> Dict[str, Any]:
      """LLM语义验证"""
      pass

  def validate_actual_fields(schema_dict: Dict[str, Any]) -> tuple:
      """验证字段在实际模型中的存在性"""
      pass

  任务 1.2: 增强字段验证机制

  估时: 1天负责人: 开发者A

  详细要求:
  - 修改flight_query_schema.py中的验证器
  - 集成introspect_models实时获取字段信息
  - 替代硬编码的MODEL_FIELDS使用

  修改文件:
  - flights_workflow/tools/flight_query_schema.py
  - 重点修改FlightQuerySchemaValidator类的_validate_fields方法

  Phase 2: 工具链集成 (优先级: 高)

  任务 2.1: 更新工作流图配置

  估时: 0.5天负责人: 开发者A

  详细要求:
  - 修改flights_workflow/graph.py
  - 将新工具加入CORE_FLIGHT_TOOLS
  - 保留原有工具用于向后兼容和调试

  代码修改:
  # 导入新工具
  from flights_workflow.tools.comprehensive_query_tool import comprehensive_flight_query

  # 更新工具注册
  CORE_FLIGHT_TOOLS = {
      "generate_flight_query": generate_flight_query,
      "comprehensive_flight_query": comprehensive_flight_query,  # 新增
      # ... 其他工具
  }

  任务 2.2: 优化System Prompt

  估时: 0.5天负责人: 开发者A

  详细要求:
  - 修改graph.py中的系统提示词
  - 简化工作流程说明：3步 → 2步
  - 强调新工具的使用方式

  Phase 3: Few-shot示例优化 (优先级: 中)

  任务 3.1: 基于实际数据重写示例

  估时: 1天负责人: 开发者B

  详细要求:
  - 修改generate_query_schema_tool.py中的get_few_shot_examples函数
  - 使用introspect_models和sample_data获取真实字段和数据
  - 确保所有示例使用实际存在的字段

  验证标准:
  - 所有Few-shot示例必须能通过新的字段验证
  - 示例覆盖常见查询模式（基础查询、聚合查询、时间范围查询等）

  任务 3.2: 语义验证提示词优化

  估时: 1天负责人: 开发者B

  详细要求:
  - 设计专门的语义验证提示词模板
  - 包含评估维度和返回格式规范
  - 添加常见错误模式的识别规则

  Phase 4: 测试和验证 (优先级: 高)

  任务 4.1: 单元测试编写

  估时: 2天负责人: 开发者B

  测试文件:
  - tests/test_comprehensive_query_tool.py
  - tests/test_field_validation.py
  - tests/test_semantic_validation.py

  测试覆盖:
  - 语法验证的各种错误情况
  - 语义验证的正确性
  - 工具链的端到端测试

  任务 4.2: 集成测试和性能测试

  估时: 1天负责人: 开发者B

  测试要求:
  - 准确率基准测试（目标70%+）
  - 响应时间测试
  - 错误恢复机制测试

  💻 代码实现指南
  关键文件代码模板

  1. comprehensive_query_tool.py (新文件)

  """
  综合查询工具 - 语法验证 + 语义验证 + 执行
  """

  import json
  import logging
  from typing import Dict, Any, List, Tuple
  from langchain_core.tools import tool
  from langchain_google_genai import ChatGoogleGenerativeAI

  from .flight_query_schema import validate_flight_query
  from .query_parser import parse_flight_query, QueryParseError
  from .introspect_models import introspect_model

  # 配置日志
  logger = logging.getLogger(__name__)

  @tool
  def comprehensive_flight_query(query_schema: str, user_question: str) -> str:
      """
      完整的航班查询工具：语法验证 → 语义验证 → 执行
      
      Args:
          query_schema: JSON格式的查询Schema
          user_question: 用户的原始问题
          
      Returns:
          str: JSON格式的查询结果或详细错误信息
      """
      try:
          logger.info(f"开始处理查询: {user_question}")

          # Step 1: 本地语法验证（快速）
          syntax_result = validate_syntax_locally(query_schema)
          if not syntax_result["valid"]:
              logger.warning(f"语法验证失败: {syntax_result['error']}")
              return format_error_response("syntax_validation", syntax_result["error"],
                                         suggestions=syntax_result.get("suggestions", []))

          generated_sql = syntax_result["sql"]
          logger.info(f"生成SQL: {generated_sql}")

          # Step 2: LLM语义验证
          semantic_result = validate_semantics_with_llm(query_schema, user_question, generated_sql)
          if not semantic_result["valid"]:
              logger.warning(f"语义验证失败: {semantic_result['issues']}")
              return format_error_response("semantic_validation", semantic_result["issues"],
                                         suggestions=semantic_result.get("suggestions", []),
                                         generated_sql=generated_sql)

          # Step 3: 执行查询
          execution_result = execute_validated_query(query_schema)
          logger.info("查询执行成功")
          return execution_result

      except Exception as e:
          logger.error(f"查询处理异常: {str(e)}")
          return format_error_response("execution", f"执行失败: {str(e)}")


  def validate_syntax_locally(query_schema: str) -> Dict[str, Any]:
      """
      本地语法验证
      
      验证内容:
      1. JSON格式正确性
      2. Schema结构验证
      3. 实际字段存在性验证
      4. SQL生成可行性验证
      """
      try:
          # JSON格式验证
          schema_dict = json.loads(query_schema)

          # Schema结构验证
          is_valid, error_msg = validate_flight_query(schema_dict)
          if not is_valid:
              return {
                  "valid": False,
                  "error": f"Schema验证失败: {error_msg}",
                  "suggestions": ["检查Schema格式是否符合FLIGHT_QUERY_SCHEMA规范"]
              }

          # 实际字段验证
          field_valid, field_error, available_fields = validate_actual_fields(schema_dict)
          if not field_valid:
              return {
                  "valid": False,
                  "error": field_error,
                  "suggestions": [f"可用字段: {available_fields[:10]}..."]
              }

          # SQL生成测试
          try:
              queryset = parse_flight_query(schema_dict)
              sql = str(queryset.query)
              return {"valid": True, "sql": sql}
          except Exception as e:
              return {
                  "valid": False,
                  "error": f"SQL生成失败: {str(e)}",
                  "suggestions": ["检查查询条件的字段名和操作符是否正确"]
              }

      except json.JSONDecodeError as e:
          return {
              "valid": False,
              "error": f"JSON格式错误: {str(e)}",
              "suggestions": ["检查JSON语法，确保所有括号和引号匹配"]
          }


  def validate_semantics_with_llm(query_schema: str, user_question: str, 
                                  generated_sql: str) -> Dict[str, Any]:
      """
      LLM语义验证
      
      使用LLM分析SQL是否能正确回答用户问题
      """
      validation_prompt = f"""
  请分析以下SQL查询是否能正确回答用户的问题。

  用户问题: {user_question}
  查询Schema: {query_schema}
  生成的SQL: {generated_sql}

  评估维度:
  1. 字段选择: 查询的字段是否足够回答用户问题
  2. 过滤条件: WHERE条件是否符合用户意图
  3. 聚合逻辑: 如有聚合，是否正确（GROUP BY, 聚合函数等）
  4. 排序设置: ORDER BY是否有助于回答问题
  5. 数据范围: 查询范围是否合适

  请严格按照以下JSON格式返回结果:
  {{
      "valid": true/false,
      "issues": ["具体问题1", "具体问题2"],
      "suggestions": ["改进建议1", "改进建议2"],
      "confidence": 0.0-1.0
  }}

  注意:
  - 如果查询逻辑完全符合用户意图，设置valid=true
  - 如果存在明显的逻辑错误或无法回答用户问题，设置valid=false
  - issues应具体说明发现的问题
  - suggestions应提供可行的改进建议
  """

      try:
          import os
          llm = ChatGoogleGenerativeAI(
              model="gemini-2.0-flash",
              temperature=0,
              google_api_key=os.getenv("GOOGLE_API_KEY")
          )
          response = llm.invoke(validation_prompt)
          result = json.loads(response.content)

          # 验证返回格式
          if not all(key in result for key in ["valid", "issues", "suggestions"]):
              raise ValueError("LLM返回格式不正确")

          return result

      except Exception as e:
          logger.warning(f"LLM语义验证失败: {str(e)}, 默认通过")
          # 如果LLM验证失败，默认通过以避免阻塞
          return {
              "valid": True,
              "issues": [],
              "suggestions": [],
              "note": f"语义验证失败，默认通过: {str(e)}"
          }


  def validate_actual_fields(schema_dict: Dict[str, Any]) -> Tuple[bool, str, List[str]]:
      """
      验证字段在实际模型中的存在性
      
      Returns:
          Tuple[bool, str, List[str]]: (是否有效, 错误信息, 可用字段列表)
      """
      try:
          model_name = schema_dict["model"]
          model_info = introspect_model(model_name, include_relationships=False)

          if "error" in model_info:
              return False, f"模型内省失败: {model_info['error']}", []

          available_fields = list(model_info["fields"].keys())

          # 检查conditions中的字段
          conditions = schema_dict.get("conditions", {})
          invalid_fields = check_fields_in_conditions(conditions, set(available_fields))

          if invalid_fields:
              error_msg = f"字段不存在: {invalid_fields}"
              return False, error_msg, available_fields

          return True, "", available_fields

      except Exception as e:
          return False, f"字段验证异常: {str(e)}", []


  def check_fields_in_conditions(conditions: Dict[str, Any], 
                                available_fields: set) -> List[str]:
      """递归检查条件中的字段"""
      invalid_fields = []

      for key, value in conditions.items():
          if key in ["AND", "OR"] and isinstance(value, list):
              for condition in value:
                  if isinstance(condition, dict):
                      invalid_fields.extend(
                          check_fields_in_conditions(condition, available_fields)
                      )
          elif key == "field" and isinstance(value, str):
              if value not in available_fields:
                  invalid_fields.append(value)
          elif isinstance(value, dict):
              invalid_fields.extend(
                  check_fields_in_conditions(value, available_fields)
              )

      return invalid_fields


  def execute_validated_query(query_schema: str) -> str:
      """执行已验证的查询"""
      try:
          schema_dict = json.loads(query_schema)
          queryset = parse_flight_query(schema_dict)

          # 处理不同类型的查询结果
          if isinstance(queryset, dict):
              # 全局聚合
              result = {
                  "success": True,
                  "query_type": "global_aggregation",
                  "result": queryset,
                  "count": 1
              }
          elif isinstance(queryset, list):
              # 分组聚合
              result = {
                  "success": True,
                  "query_type": "grouped_aggregation",
                  "result": queryset,
                  "count": len(queryset)
              }
          else:
              # 普通查询
              results = list(queryset.values())
              result = {
                  "success": True,
                  "query_type": "normal",
                  "result": results,
                  "count": len(results)
              }

          return json.dumps(result, ensure_ascii=False, indent=2, default=str)

      except Exception as e:
          raise QueryParseError(f"查询执行失败: {str(e)}")


  def format_error_response(stage: str, error: str, suggestions: List[str] = None, 
                           generated_sql: str = None) -> str:
      """格式化错误响应"""
      response = {
          "success": False,
          "stage": stage,
          "error": error,
          "suggestions": suggestions or []
      }

      if generated_sql:
          response["generated_sql"] = generated_sql

      return json.dumps(response, ensure_ascii=False, indent=2)

  2. 测试文件模板

  # tests/test_comprehensive_query_tool.py
  """
  综合查询工具测试
  """

  import pytest
  import json
  from flights_workflow.tools.comprehensive_query_tool import (
      comprehensive_flight_query,
      validate_syntax_locally,
      validate_semantics_with_llm,
      validate_actual_fields
  )

  class TestComprehensiveQueryTool:

      def test_valid_query_execution(self):
          """测试有效查询的完整执行流程"""
          query_schema = json.dumps({
              "model": "aviation_flight",
              "conditions": {
                  "AND": [
                      {"field": "airline_name", "operator": "contains", "value": "东方"}
                  ]
              },
              "limit": 10
          })
          user_question = "查询东方航空的航班"

          result = comprehensive_flight_query(query_schema, user_question)
          result_dict = json.loads(result)

          assert result_dict["success"] == True
          assert "result" in result_dict

      def test_syntax_validation_invalid_field(self):
          """测试语法验证 - 无效字段"""
          query_schema = json.dumps({
              "model": "aviation_flight",
              "conditions": {
                  "AND": [
                      {"field": "invalid_field", "operator": "exact", "value": "test"}
                  ]
              }
          })

          result = validate_syntax_locally(query_schema)
          assert result["valid"] == False
          assert "invalid_field" in result["error"]

      def test_semantic_validation(self):
          """测试语义验证"""
          query_schema = '{"model": "aviation_flight", "conditions": {}}'
          user_question = "查询航班信息"
          generated_sql = "SELECT * FROM aviation_flight LIMIT 100"

          result = validate_semantics_with_llm(query_schema, user_question, generated_sql)

          assert "valid" in result
          assert "issues" in result
          assert "suggestions" in result

      def test_field_validation_success(self):
          """测试字段验证成功情况"""
          schema_dict = {
              "model": "aviation_flight",
              "conditions": {
                  "AND": [
                      {"field": "airline_name", "operator": "contains", "value": "test"}
                  ]
              }
          }

          valid, error, available_fields = validate_actual_fields(schema_dict)
          assert valid == True
          assert error == ""
          assert len(available_fields) > 0

  🧪 测试计划和验收标准
  测试策略

  1. 单元测试 (覆盖率 ≥ 90%)

  测试文件结构:
  tests/
  ├── test_comprehensive_query_tool.py     # 综合查询工具测试
  ├── test_syntax_validation.py            # 语法验证测试  
  ├── test_semantic_validation.py          # 语义验证测试
  ├── test_field_validation.py             # 字段验证测试
  └── test_integration.py                  # 集成测试

  关键测试用例:
  - 有效查询的完整流程
  - 各种语法错误的捕获
  - 语义验证的准确性
  - 错误恢复机制
  - 边界条件处理

  2. 准确率基准测试

  测试数据集:
  # 准备100个标准测试用例
  TEST_CASES = [
      {
          "user_query": "查询国航从北京到上海的航班",
          "expected_fields": ["airline_name", "from_airport__name", "to_airport__name"],
          "expected_conditions": ["contains", "contains", "contains"],
          "category": "basic_query"
      },
      {
          "user_query": "统计各航空公司的航班数量",
          "expected_aggregation": True,
          "expected_group_by": ["airline_name"],
          "category": "aggregation_query"
      },
      # ... 更多测试用例
  ]

  准确率计算:
  def calculate_accuracy(test_results):
      """
      准确率计算标准:
      - 完全正确: 1.0分
      - 语法正确但语义有问题: 0.5分  
      - 语法错误: 0.0分
      """
      total_score = sum(result.score for result in test_results)
      return total_score / len(test_results)

  3. 性能测试

  测试指标:
  - 平均响应时间 < 5秒
  - 99%请求响应时间 < 10秒
  - 语法验证时间 < 0.5秒
  - 语义验证时间 < 3秒

  4. 集成测试

  完整工作流测试:
  def test_end_to_end_workflow():
      """测试完整的ReAct工作流"""
      test_queries = [
          "查询今天延误的航班",
          "统计各机场的航班流量",
          "查询波音737的航班信息"
      ]

      for query in test_queries:
          # 模拟完整的LLM+工具调用流程
          result = simulate_react_workflow(query)
          assert result["success"] == True

  验收标准

  核心指标 (必须达成)

  1. 准确率提升: 从30% → ≥70%
  2. 语法验证准确率: ≥95%
  3. 语义验证合理性: ≥80%
  4. 系统稳定性: 错误率 <5%

  功能要求 (必须实现)

  ✅ 语法验证功能
  - JSON Schema格式验证
  - 实际字段存在性验证
  - SQL生成可行性验证

  ✅ 语义验证功能
  - LLM语义分析
  - 用户意图匹配检查
  - 查询逻辑合理性评估

  ✅ 统一错误处理
  - 分阶段错误定位
  - 详细错误信息
  - 改进建议提供

  ✅ 向后兼容性
  - 保留原有工具接口
  - 现有测试用例通过
  - 无破坏性更改

  性能要求 (必须满足)

  - 平均查询响应时间 ≤ 5秒
  - 内存使用无明显增长
  - 数据库连接池稳定

  代码质量要求

  - 代码覆盖率 ≥ 90%
  - 所有新增代码通过Pylint检查
  - 完整的文档和注释
  - 遵循现有代码规范

  测试执行计划

  阶段一: 开发测试 (开发期间)

  - 单元测试持续集成
  - 代码质量检查
  - 基本功能验证

  阶段二: 系统测试 (开发完成后)

  - 完整功能测试
  - 准确率基准测试
  - 性能测试
  - 兼容性测试

  阶段三: 用户验收测试 (交付前)

  - 真实用例测试
  - 用户体验评估
  - 准确率最终验证

  ⚠️ 风险评估和应对策略

  高风险项目

  1. LLM语义验证不稳定

  风险: LLM返回格式不一致，验证结果不可靠
  应对:
  - 设计稳定的提示词模板
  - 添加返回格式验证
  - 提供fallback机制

  2. 准确率提升不达预期

  风险: 实际准确率提升低于70%目标
  应对:
  - 分阶段验证，及时调整策略
  - 增加Few-shot示例数量和质量
  - 优化语义验证算法

  3. 性能回归

  风险: 新增语义验证导致响应时间增加
  应对:
  - 并行处理语法和语义验证
  - 优化LLM调用次数
  - 添加缓存机制

  中风险项目

  1. 字段验证覆盖不全

  风险: 动态字段获取遗漏某些情况
  应对: 增加字段验证的测试用例

  2. 错误处理复杂化

  风险: 统一错误处理增加系统复杂性
  应对: 详细的错误处理文档和测试

  📅 项目时间表

  总体时间: 2周

  第1周

  - Day 1-2: Phase 1 - 创建comprehensive_query_tool.py
  - Day 3: Phase 2 - 工具链集成和配置更新
  - Day 4-5: Phase 3 - Few-shot示例优化

  第2周

  - Day 6-7: Phase 4 - 测试编写和执行
  - Day 8-9: 集成测试和性能优化
  - Day 10: 文档完善和交付准备

  里程碑检查点

  - Week 1 End: 核心功能开发完成，基础测试通过
  - Week 2 Mid: 准确率达到60%+
  - Week 2 End: 所有验收标准达成，准备交付

