"""
Django settings for flights_qa_system project.

Generated by 'django-admin startproject' using Django 5.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

load_dotenv()

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-h_zemnp6h+%!$sq-^ts8f48i(fj6$5(qa$8xc$g18v&zu518+9"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "flights",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "flights_qa_system.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "flights_qa_system.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# Local development PostgreSQL configuration with PostGIS support
LOCAL_DB = {
    "ENGINE": "django.contrib.gis.db.backends.postgis",
    "NAME": os.getenv("LOCAL_DB_NAME", "flights_local"),
    "USER": os.getenv("LOCAL_DB_USER", "postgres"),
    "PASSWORD": os.getenv("LOCAL_DB_PASSWORD", ""),
    "HOST": os.getenv("LOCAL_DB_HOST", "localhost"),
    "PORT": os.getenv("LOCAL_DB_PORT", "5432"),
}

# Remote development PostgreSQL configuration with PostGIS support
REMOTE_DB = {
    "ENGINE": "django.contrib.gis.db.backends.postgis",
    "NAME": os.getenv("REMOTE_DB_NAME", "flights_remote"),
    "USER": os.getenv("REMOTE_DB_USER", "postgres"),
    "PASSWORD": os.getenv("REMOTE_DB_PASSWORD", ""),
    "HOST": os.getenv("REMOTE_DB_HOST", "remote-db-host"),
    "PORT": os.getenv("REMOTE_DB_PORT", "5432"),
}

# Production PostgreSQL configuration with PostGIS support
PROD_DB = {
    "ENGINE": "django.contrib.gis.db.backends.postgis",
    "NAME": os.getenv("PROD_DB_NAME", "flights_prod"),
    "USER": os.getenv("PROD_DB_USER", "postgres"),
    "PASSWORD": os.getenv("PROD_DB_PASSWORD", ""),
    "HOST": os.getenv("PROD_DB_HOST", "prod-db-host"),
    "PORT": os.getenv("PROD_DB_PORT", "5432"),
}

# Database selection based on environment
DB_CONFIG_MAP = {
    "local": LOCAL_DB,
    "remote": REMOTE_DB,
    "production": PROD_DB,
}

# Environment selection: local, remote, production
ENVIRONMENT = os.getenv("ENVIRONMENT", "local").lower()

# Validate environment setting
if ENVIRONMENT not in DB_CONFIG_MAP:
    raise ValueError(f"Invalid ENVIRONMENT setting: {ENVIRONMENT}. Must be one of: {list(DB_CONFIG_MAP.keys())}")

# Compose multi-database setting and select default based on ENVIRONMENT
DATABASES = {
    "default": DB_CONFIG_MAP[ENVIRONMENT],
    "local": LOCAL_DB,
    "remote": REMOTE_DB,
    "production": PROD_DB,
}

# Set DEBUG based on environment (can be overridden by DEBUG env var)
if not os.getenv("DEBUG"):
    DEBUG = ENVIRONMENT != "production"


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
