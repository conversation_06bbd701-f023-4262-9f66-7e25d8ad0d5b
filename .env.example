# Environment variables for the Flights QA System

# Google Gemini API Key
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your-gemini-api-key-here

# Django settings
# Environment selection: local, remote, production
ENVIRONMENT=local
DEBUG=True
SECRET_KEY=your-secret-key-here

# Local development database (PostgreSQL)
LOCAL_DB_NAME=flights_local
LOCAL_DB_USER=postgres
LOCAL_DB_PASSWORD=your-local-db-password
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=5432

# Remote development database (PostgreSQL)
REMOTE_DB_NAME=flights_remote
REMOTE_DB_USER=postgres
REMOTE_DB_PASSWORD=your-remote-db-password
REMOTE_DB_HOST=remote-db-host
REMOTE_DB_PORT=5432

# Production database (PostgreSQL)
PROD_DB_NAME=flights_prod
PROD_DB_USER=postgres
PROD_DB_PASSWORD=your-prod-db-password
PROD_DB_HOST=prod-db-host
PROD_DB_PORT=5432
