"""
模型内省工具 - 获取Django模型的结构信息
"""

import os
import django
import json
from typing import Dict, Any, List

# 配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from django.contrib.gis.db.models.fields import GeometryField
from django.contrib.postgres.fields import ArrayField
from django.db.models.fields.related import ForeignKey, ManyToManyField, OneToOneField
from django.db.models.fields.reverse_related import ManyToOneRel, ManyToManyRel, OneToOneRel

from flights.models import (
    aviation_airports, aviation_aircraft, aviation_flight
)

# 模型映射
AVAILABLE_MODELS = {
    'aviation_airports': aviation_airports,
    'aviation_aircraft': aviation_aircraft,
    'aviation_flight': aviation_flight,
}

# 大数据量表，跳过count统计
LARGE_TABLES = {'aviation_flight'}


def introspect_model(model_name: str, include_relationships: bool = True, 
                    include_meta: bool = True, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    内省指定Django模型的结构信息
    
    Args:
        model_name: 模型名称
        include_relationships: 是否包含关系字段信息
        include_meta: 是否包含模型元信息
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 包含模型结构信息的字典
    """
    if model_name not in AVAILABLE_MODELS:
        return {
            "error": f"模型 '{model_name}' 不存在",
            "available_models": list(AVAILABLE_MODELS.keys())
        }
    
    model_class = AVAILABLE_MODELS[model_name]
    
    try:
        # 基本信息
        result = {
            "model_name": model_name,
            "verbose_name": str(model_class._meta.verbose_name),
            "fields": {},
            "primary_key": None,
            "indexes": [],
        }
        
        # 获取字段信息
        for field in model_class._meta.get_fields():
            # 过滤系统字段
            if exclude_sys_fields and field.name.startswith('sys_'):
                continue
            
            # 跳过反向关系字段（除非明确要求包含关系）
            if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
                if not include_relationships:
                    continue
                field_info = _get_reverse_relation_info(field)
            else:
                field_info = _get_field_info(field)
            
            result["fields"][field.name] = field_info
            
            # 标记主键
            if hasattr(field, 'primary_key') and field.primary_key:
                result["primary_key"] = field.name
        
        # 获取索引信息
        if include_meta:
            result["indexes"] = _get_indexes_info(model_class)
        
        return result
        
    except Exception as e:
        return {
            "error": f"内省模型失败: {str(e)}",
            "model_name": model_name
        }


def _get_field_info(field) -> Dict[str, Any]:
    """获取字段详细信息"""
    field_info = {
        "type": field.__class__.__name__,
        "verbose_name": str(getattr(field, 'verbose_name', field.name)),
    }

    # 字段help_text
    if hasattr(field, 'help_text') and field.help_text:
        field_info["help_text"] = str(field.help_text)
    
    # 数字字段的范围
    if hasattr(field, 'max_digits'):
        field_info["max_digits"] = field.max_digits
    if hasattr(field, 'decimal_places'):
        field_info["decimal_places"] = field.decimal_places
    
    # 选择项
    if hasattr(field, 'choices') and field.choices:
        field_info["choices"] = list(field.choices)
    
    # 外键关系
    if isinstance(field, ForeignKey):
        field_info["related_model"] = field.related_model.__name__
        field_info["related_field"] = field.target_field.name
    
    # 多对多关系
    elif isinstance(field, ManyToManyField):
        field_info["related_model"] = field.related_model.__name__
        field_info["through"] = field.remote_field.through.__name__ if field.remote_field.through else None
    
    # 一对一关系
    elif isinstance(field, OneToOneField):
        field_info["related_model"] = field.related_model.__name__
        field_info["on_delete"] = str(field.remote_field.on_delete)
    
    # 数组字段
    elif isinstance(field, ArrayField):
        field_info["base_field"] = field.base_field.__class__.__name__
        if hasattr(field.base_field, 'max_length'):
            field_info["base_field_max_length"] = field.base_field.max_length
    
    # 几何字段
    elif isinstance(field, GeometryField):
        field_info["srid"] = getattr(field, 'srid', None)
        field_info["dim"] = getattr(field, 'dim', None)
        field_info["geography"] = getattr(field, 'geography', False)
    
    return field_info


def _get_reverse_relation_info(field) -> Dict[str, Any]:
    """获取反向关系字段信息"""
    return {
        "type": field.__class__.__name__,
        "related_model": field.related_model.__name__,
        "related_name": field.get_accessor_name(),
        "field_name": field.field.name,
        "multiple": isinstance(field, (ManyToManyRel, ManyToOneRel)),
        "is_reverse": True
    }


def _get_indexes_info(model_class) -> List[str]:
    """获取有索引的字段名列表"""
    indexed_fields = set()
    
    # 从模型定义的索引中获取字段
    for index in model_class._meta.indexes:
        indexed_fields.update(index.fields)
    
    # 从字段的db_index属性中获取
    for field in model_class._meta.get_fields():
        if hasattr(field, 'db_index') and field.db_index:
            indexed_fields.add(field.name)
    
    return sorted(list(indexed_fields))


def introspect_models_tool(model_names: str, 
                          include_relationships: bool = True,
                          include_meta: bool = True,
                          exclude_sys_fields: bool = True) -> str:
    """
    提供多Django模型处理的内省函数
    
    Args:
        model_names: 逗号分隔的模型名称字符串
        include_relationships: 是否包含关系字段信息
        include_meta: 是否包含模型元信息
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        str: JSON格式的模型结构信息
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({
            "error": "必须指定至少一个模型名称",
            "available_models": list(AVAILABLE_MODELS.keys())
        }, ensure_ascii=False, indent=2)
    
    if len(model_list) == 1:
        # 单个模型，返回详细信息
        result = introspect_model(
            model_list[0],
            include_relationships=include_relationships,
            include_meta=include_meta,
            exclude_sys_fields=exclude_sys_fields
        )
    else:
        # 多个模型，返回每个模型的信息
        result = {}
        for model_name in model_list:
            result[model_name] = introspect_model(
                model_name,
                include_relationships=include_relationships,
                include_meta=include_meta,
                exclude_sys_fields=exclude_sys_fields
            )
    
    return json.dumps(result, ensure_ascii=False, indent=2, default=str)


if __name__ == "__main__":
    # 测试代码
    print("=== 航班模型详细信息 ===")
    print(introspect_models_tool("aviation_flight"))
    
    print("\n=== 机场模型详细信息 ===")
    print(introspect_models_tool("aviation_airports"))