"""
样例数据工具 - 获取Django模型的样例数据
"""

import os
import django
import json
from typing import Dict, Any, List
from decimal import Decimal
from datetime import datetime, date

# 配置Django设置
# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
# django.setup()

from django.core.serializers.json import DjangoJSONEncoder
from django.contrib.gis.geos import GEOSGeometry
from django.contrib.gis.db.models.fields import Geo<PERSON><PERSON>ield
from django.contrib.postgres.fields import <PERSON><PERSON>yField, J<PERSON><PERSON>ield
from django.db.models.fields.related import ForeignKey
from django.db.models.fields.reverse_related import ManyToOneRel, ManyToManyRel, OneToOneRel

from flights.models import (
    aviation_airports, aviation_aircraft, aviation_flight,
    model_root, who_region, climatic_zone, data_base2, 
    admin_base, world_admin, unit_admin_amap
)

LARGE_TABLES = {aviation_flight}

# 模型映射
AVAILABLE_MODELS = {
    'aviation_airports': aviation_airports,
    'aviation_aircraft': aviation_aircraft,
    'aviation_flight': aviation_flight,
    'model_root': model_root,
    'who_region': who_region,
    'climatic_zone': climatic_zone,
    'data_base2': data_base2,
    'admin_base': admin_base,
    'world_admin': world_admin,
    'unit_admin_amap': unit_admin_amap,
}


class CustomJSONEncoder(DjangoJSONEncoder):
    """自定义JSON编码器"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, GEOSGeometry):
            return {
                'type': 'geometry',
                'geom_type': obj.geom_type,
                'wkt': str(obj)[:100] + '...' if len(str(obj)) > 100 else str(obj)
            }
        return super().default(obj)


def get_sample_data(model_name: str, limit: int = 5, offset: int = 0, 
                   random: bool = False, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    获取指定Django模型的样例数据
    
    Args:
        model_name: 模型名称
        limit: 返回记录数量限制
        offset: 跳过的记录数量（仅非随机模式有效）
        random: 是否随机获取数据
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 包含样例数据的字典
    """
    if model_name not in AVAILABLE_MODELS:
        return {
            "error": f"模型 '{model_name}' 不存在",
            "available_models": list(AVAILABLE_MODELS.keys())
        }
    
    model_class = AVAILABLE_MODELS[model_name]
    
    # 获取总记录数
    try:
        if model_class in LARGE_TABLES:
            total_count = 9999999
        else:
            total_count = model_class.objects.count()
        
    except Exception as e:
        return {
            "error": f"数据库访问错误: {str(e)}",
            "model_name": model_name,
            "table_name": model_class._meta.db_table
        }
    
    if total_count == 0:
        return {
            "model_name": model_name,
            "total_count": 0,
            "sample_data": [],
            "fields_info": _get_fields_summary(model_class, exclude_sys_fields)
        }
    
    # 获取几何字段列表
    geometry_fields = [
        field.name for field in model_class._meta.get_fields()
        if isinstance(field, GeometryField)
    ]
    
    # 构建查询集
    queryset = model_class.objects.defer(*geometry_fields) if geometry_fields else model_class.objects.all()
    
    # 应用随机或顺序获取
    if random:
        queryset = queryset.order_by('?')[:limit]
    else:
        queryset = queryset[offset:offset+limit]
    
    # 转换为字典列表
    sample_records = []
    for instance in list(queryset):
        try:
            record = _model_instance_to_dict(instance, exclude_sys_fields)
            sample_records.append(record)
        except Exception as e:
            sample_records.append({
                "error": f"记录转换失败: {str(e)}",
                "pk": getattr(instance, 'pk', None)
            })
    
    result = {
        "model_name": model_name,
        "total_count": total_count,
        "sample_count": len(sample_records),
        "sample_data": sample_records,
        "fields_info": _get_fields_summary(model_class, exclude_sys_fields)
    }
    
    if random:
        result["sample_type"] = "random"
    else:
        result["offset"] = offset
        result["limit"] = limit
    
    return result


def _model_instance_to_dict(instance, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """将Django模型实例转换为字典"""
    data = {}
    
    for field in instance._meta.get_fields():
        # 过滤系统字段
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue
        
        # 跳过反向关系字段
        if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
            continue
        
        # 处理几何字段
        if isinstance(field, GeometryField):
            data[field.name] = {'type': 'geometry', 'note': '几何数据已省略'}
            continue
        
        try:
            if isinstance(field, ForeignKey):
                # 获取外键ID值
                data[field.name] = getattr(instance, field.attname, None)
            else:
                data[field.name] = getattr(instance, field.name)
        except Exception as e:
            data[field.name] = f"<错误: {str(e)}>"
    
    return data


def _get_fields_summary(model_class, exclude_sys_fields: bool = True) -> Dict[str, str]:
    """
    获取模型字段摘要信息

    Args:
        model_class: Django模型类
        exclude_sys_fields: 是否排除系统字段

    Returns:
        Dict: 字段名到字段类型的映射
    """
    fields_info = {}

    for field in model_class._meta.get_fields():
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue

        # 跳过反向关系字段
        if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
            continue

        # 为不同字段类型提供更详细的信息
        if isinstance(field, ForeignKey):
            fields_info[field.name] = f"ForeignKey -> {field.related_model.__name__}"
        elif isinstance(field, GeometryField):
            fields_info[field.name] = f"GeometryField (SRID: {getattr(field, 'srid', 'unknown')})"
        elif isinstance(field, ArrayField):
            base_field_type = field.base_field.__class__.__name__
            fields_info[field.name] = f"ArrayField[{base_field_type}]"
        elif isinstance(field, JSONField):
            fields_info[field.name] = "JSONField"
        else:
            fields_info[field.name] = field.__class__.__name__

    return fields_info


def get_sample_data_tool(model_names: str, limit: int = 5, offset: int = 0, random: bool = False) -> str:
    """
    提供django model样例数据的函数
    
    Args:
        model_names: 逗号分隔的模型名称字符串
        limit: 每个模型返回的记录数量限制
        offset: 跳过的记录数量（仅非随机模式有效）
        random: 是否随机获取
        
    Returns:
        str: JSON格式的样例数据
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({
            "error": "请提供要获取样例数据的模型名称",
            "available_models": list(AVAILABLE_MODELS.keys())
        }, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    results = {}
    for model_name in model_list:
        results[model_name] = get_sample_data(model_name, limit, offset, random)
    
    return json.dumps(results, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)

if __name__ == "__main__":
    print(get_sample_data_tool('aviation_airports, aviation_aircraft, aviation_flight', limit=3, offset=1000))
