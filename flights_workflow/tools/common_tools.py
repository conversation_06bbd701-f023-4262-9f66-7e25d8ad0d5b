import json
from datetime import datetime, timedelta
from langchain_core.tools import tool


@tool
def get_current_time() -> str:
    """获取当前时间"""
    current_time = datetime.now()
    return f"当前时间是: {current_time.strftime('%Y-%m-%d %H:%M:%S')}"


@tool
def get_weather(location: str) -> str:
    """获取指定地点的天气信息
    
    Args:
        location: 地点名称，如"北京"、"上海"等
    """
    # 这是一个模拟的天气工具，实际应用中应该调用真实的天气API
    weather_data = {
        "北京": "晴天，温度 15°C，微风",
        "上海": "多云，温度 18°C，东南风",
        "广州": "小雨，温度 22°C，南风",
        "深圳": "阴天，温度 20°C，无风"
    }
    
    return weather_data.get(location, f"抱歉，暂时无法获取{location}的天气信息")


@tool
def get_date_range_for_recent_days(days: int = 7) -> str:
    """
    获取最近N天的日期范围
    
    Args:
        days: 天数，默认7天
        
    Returns:
        str: JSON格式的日期范围信息
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    result = {
        "days": days,
        "start_date": start_date.strftime('%Y-%m-%d'),
        "end_date": end_date.strftime('%Y-%m-%d'),
        "start_datetime": start_date.strftime('%Y-%m-%dT%H:%M:%S'),
        "end_datetime": end_date.strftime('%Y-%m-%dT%H:%M:%S'),
        "description": f"最近{days}天的日期范围"
    }
    
    return json.dumps(result, ensure_ascii=False, indent=2)


def get_current_date() -> str:
    """获取当前日期"""
    return datetime.now().strftime('%Y-%m-%d')