"""
统一查询验证器

集中处理所有航班查询的验证逻辑，包括：
- JSON Schema结构验证
- 字段存在性验证（包括跨表字段）
- 聚合和注解规则验证
- 业务逻辑验证

这个文件取代了分散在flight_query_schema.py、comprehensive_query_tool.py、
query_parser.py中的重复验证实现。
"""

import json
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
import jsonschema
from jsonschema import validate, ValidationError

from .flight_query_schema import (
    FLIGHT_QUERY_SCHEMA,
    SUPPORTED_MODELS,
    QUERY_OPERATORS,
    AGGREGATE_FUNCTIONS,
    ANNOTATION_FUNCTIONS
)


@dataclass
class ValidationResult:
    """验证结果数据类"""
    valid: bool
    errors: List[str]
    warnings: List[str]
    field_info: Dict[str, Any]
    query_complexity: str
    validation_details: Dict[str, Any]
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.errors:
            self.errors = []
        if not self.warnings:
            self.warnings = []
        if not self.field_info:
            self.field_info = {}
        if not self.validation_details:
            self.validation_details = {}


class QueryValidator:
    """
    统一查询验证器类
    
    集中处理所有查询验证逻辑，提供清晰的验证接口和详细的验证结果。
    """
    
    def __init__(self):
        self.schema = FLIGHT_QUERY_SCHEMA
        self.supported_models = SUPPORTED_MODELS
        self.query_operators = QUERY_OPERATORS
        self.aggregate_functions = AGGREGATE_FUNCTIONS
        self.annotation_functions = ANNOTATION_FUNCTIONS
        self._field_cache = {}  # 字段信息缓存
    
    def validate_complete(self, query_data: Dict[str, Any]) -> ValidationResult:
        """
        完整验证查询条件
        
        Args:
            query_data: 待验证的查询条件字典
            
        Returns:
            ValidationResult: 详细验证结果
        """
        result = ValidationResult(
            valid=True,
            errors=[],
            warnings=[],
            field_info={},
            query_complexity="unknown",
            validation_details={}
        )
        
        try:
            # 1. JSON Schema结构验证
            schema_valid, schema_errors = self._validate_schema_structure(query_data)
            if not schema_valid:
                result.valid = False
                result.errors.extend(schema_errors)
                result.validation_details["schema_validation"] = False
                return result
            
            result.validation_details["schema_validation"] = True
            
            # 2. 模型支持验证
            model_name = query_data.get("model")
            if model_name not in self.supported_models:
                result.valid = False
                result.errors.append(f"不支持的模型: {model_name}")
                result.validation_details["model_validation"] = False
                return result
            
            result.validation_details["model_validation"] = True
            
            # 3. 字段存在性验证
            field_valid, field_errors, field_info = self._validate_field_existence(query_data, model_name)
            result.field_info = field_info
            if not field_valid:
                result.valid = False
                result.errors.extend(field_errors)
                result.validation_details["field_validation"] = False
            else:
                result.validation_details["field_validation"] = True
            
            # 4. 聚合查询验证
            if "aggregations" in query_data:
                agg_valid, agg_errors = self._validate_aggregations(query_data["aggregations"], model_name)
                if not agg_valid:
                    result.valid = False
                    result.errors.extend(agg_errors)
                    result.validation_details["aggregation_validation"] = False
                else:
                    result.validation_details["aggregation_validation"] = True
            
            # 5. 注解查询验证
            if "annotations" in query_data:
                ann_valid, ann_errors = self._validate_annotations(query_data["annotations"], model_name)
                if not ann_valid:
                    result.valid = False
                    result.errors.extend(ann_errors)
                    result.validation_details["annotation_validation"] = False
                else:
                    result.validation_details["annotation_validation"] = True
            
            # 6. 注解后条件验证
            if "conditions_after_annotations" in query_data:
                after_valid, after_errors = self._validate_conditions_after_annotations(
                    query_data["conditions_after_annotations"], 
                    query_data.get("annotations", {}),
                    model_name
                )
                if not after_valid:
                    result.valid = False
                    result.errors.extend(after_errors)
                    result.validation_details["conditions_after_annotations_validation"] = False
                else:
                    result.validation_details["conditions_after_annotations_validation"] = True
            
            # 7. 业务规则验证
            business_valid, business_warnings = self._validate_business_rules(query_data)
            result.warnings.extend(business_warnings)
            result.validation_details["business_validation"] = business_valid
            
            # 8. 查询复杂度评估
            result.query_complexity = self._assess_query_complexity(query_data.get("conditions", {}))
            
            return result
            
        except Exception as e:
            result.valid = False
            result.errors.append(f"验证过程出错: {str(e)}")
            result.validation_details["validation_exception"] = str(e)
            return result
    
    def _validate_schema_structure(self, query_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """JSON Schema结构验证"""
        try:
            validate(instance=query_data, schema=self.schema)
            return True, []
        except ValidationError as e:
            return False, [f"Schema验证失败: {e.message}"]
        except Exception as e:
            return False, [f"Schema验证异常: {str(e)}"]
    
    def _validate_field_existence(self, query_data: Dict[str, Any], model_name: str) -> tuple[bool, List[str], Dict[str, Any]]:
        """验证字段在模型中的存在性"""
        try:
            # 获取模型字段信息（使用缓存）
            model_fields = self._get_model_fields(model_name)
            if not model_fields:
                return False, [f"无法获取模型 {model_name} 的字段信息"], {}
            
            errors = []
            
            # 验证conditions中的字段
            conditions = query_data.get("conditions", {})
            if conditions:
                cond_errors = self._validate_fields_in_conditions(conditions, set(model_fields.keys()))
                errors.extend(cond_errors)
            
            # 验证ordering中的字段（包括注解字段和聚合字段）
            ordering = query_data.get("ordering", [])
            annotation_fields = set(query_data.get("annotations", {}).keys())
            aggregation_fields = set()
            if "aggregations" in query_data:
                aggregation_fields = set(query_data["aggregations"].get("functions", {}).keys())
            
            for order_field in ordering:
                clean_field = order_field.lstrip('-')  # 移除排序前缀
                # 检查字段是否存在于模型字段、注解字段或聚合字段中
                if (clean_field not in model_fields and 
                    clean_field not in annotation_fields and 
                    clean_field not in aggregation_fields):
                    errors.append(f"排序字段 '{clean_field}' 不存在于模型 '{model_name}' 中，且不是注解或聚合字段")
            
            # 验证aggregations中的字段
            if "aggregations" in query_data:
                agg_errors = self._validate_aggregation_fields(query_data["aggregations"], model_fields, model_name)
                errors.extend(agg_errors)
            
            # 验证annotations中的字段
            if "annotations" in query_data:
                ann_errors = self._validate_annotation_fields(query_data["annotations"], model_fields, model_name)
                errors.extend(ann_errors)
            
            field_info = {
                "total_fields": len(model_fields),
                "available_fields": list(model_fields.keys())[:20],  # 限制返回数量
                "model_name": model_name
            }
            
            return len(errors) == 0, errors, field_info
            
        except Exception as e:
            return False, [f"字段验证异常: {str(e)}"], {}
    
    def _get_model_fields(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取模型字段信息（带缓存）"""
        if model_name in self._field_cache:
            return self._field_cache[model_name]
        
        try:
            from .introspect_models import introspect_model
            model_info = introspect_model(model_name, include_relationships=True)
            
            if "error" in model_info:
                return None
            
            # 构建字段字典，包括跨表字段
            model_fields = {}
            
            # 基础字段
            for field_name, field_info in model_info.get("fields", {}).items():
                model_fields[field_name] = field_info
            
            # 关系字段（跨表查询支持）
            for rel_name, rel_info in model_info.get("relationships", {}).items():
                if rel_info.get("type") in ["ForeignKey", "OneToOneField"]:
                    # 添加基础关系字段
                    model_fields[rel_name] = rel_info
                    
                    # 尝试获取关联模型的字段信息，添加跨表字段
                    related_model = rel_info.get("related_model")
                    if related_model:
                        try:
                            related_info = introspect_model(related_model, include_relationships=False)
                            for related_field_name, related_field_info in related_info.get("fields", {}).items():
                                cross_field_name = f"{rel_name}__{related_field_name}"
                                model_fields[cross_field_name] = {
                                    "type": f"CrossTable[{related_field_info.get('type', 'Unknown')}]",
                                    "related_model": related_model,
                                    "base_field": rel_name,
                                    "target_field": related_field_name
                                }
                        except Exception:
                            # 跨表字段获取失败，继续处理
                            pass
            
            # 缓存结果
            self._field_cache[model_name] = model_fields
            return model_fields
            
        except Exception:
            return None
    
    def _validate_fields_in_conditions(self, conditions: Dict[str, Any], available_fields: Set[str]) -> List[str]:
        """递归验证条件中的字段"""
        errors = []
        
        for key, value in conditions.items():
            if key in ["AND", "OR"] and isinstance(value, list):
                for condition in value:
                    if isinstance(condition, dict):
                        errors.extend(self._validate_fields_in_conditions(condition, available_fields))
            elif key == "field" and isinstance(value, str):
                # 改进跨表字段验证
                if '__' in value:
                    base_field = value.split('__')[0]
                    if base_field not in available_fields:
                        errors.append(f"字段 '{value}' 的基础字段 '{base_field}' 不存在")
                    # 如果基础字段存在，假设跨表字段有效
                else:
                    if value not in available_fields:
                        errors.append(f"字段 '{value}' 不存在")
            elif isinstance(value, dict):
                errors.extend(self._validate_fields_in_conditions(value, available_fields))
        
        return errors
    
    def _validate_aggregation_fields(self, aggregations: Dict[str, Any], model_fields: Dict[str, Any], model_name: str) -> List[str]:
        """验证聚合查询中的字段"""
        errors = []
        
        # 验证分组字段（包括跨表字段）
        group_by = aggregations.get("group_by", [])
        for field in group_by:
            # 对于跨表字段，检查基础字段是否为外键
            if '__' in field:
                base_field = field.split('__')[0]
                if base_field not in model_fields:
                    errors.append(f"分组字段 '{field}' 的基础字段 '{base_field}' 不存在于模型 '{model_name}' 中")
                # 如果基础字段存在，假设跨表字段有效（因为完整的跨表验证较复杂）
            else:
                if field not in model_fields:
                    errors.append(f"分组字段 '{field}' 不存在于模型 '{model_name}' 中")
        
        # 验证聚合函数字段
        functions = aggregations.get("functions", {})
        for func_name, func_config in functions.items():
            field = func_config.get("field")
            if field:
                if '__' in field:
                    base_field = field.split('__')[0]
                    if base_field not in model_fields:
                        errors.append(f"聚合字段 '{field}' 的基础字段 '{base_field}' 不存在于模型 '{model_name}' 中")
                else:
                    if field not in model_fields:
                        errors.append(f"聚合字段 '{field}' 不存在于模型 '{model_name}' 中")
        
        return errors
    
    def _validate_annotation_fields(self, annotations: Dict[str, Any], model_fields: Dict[str, Any], model_name: str) -> List[str]:
        """验证注解查询中的字段"""
        errors = []
        
        for ann_name, ann_config in annotations.items():
            # 验证基础字段
            field = ann_config.get("field")
            if field and field not in model_fields:
                errors.append(f"注解字段 '{field}' 不存在于模型 '{model_name}' 中")
            
            # 验证time_diff函数的特殊字段
            if ann_config.get("function") == "time_diff":
                field1 = ann_config.get("field1")
                field2 = ann_config.get("field2")
                if field1 and field1 not in model_fields:
                    errors.append(f"时间差字段1 '{field1}' 不存在于模型 '{model_name}' 中")
                if field2 and field2 not in model_fields:
                    errors.append(f"时间差字段2 '{field2}' 不存在于模型 '{model_name}' 中")
            
            # 验证窗口函数字段
            partition_by = ann_config.get("partition_by", [])
            for field in partition_by:
                if field not in model_fields:
                    errors.append(f"分区字段 '{field}' 不存在于模型 '{model_name}' 中")
            
            order_by = ann_config.get("order_by", [])
            for field in order_by:
                clean_field = field.lstrip('-')
                if clean_field not in model_fields:
                    errors.append(f"排序字段 '{clean_field}' 不存在于模型 '{model_name}' 中")
        
        return errors
    
    def _validate_aggregations(self, aggregations: Dict[str, Any], model_name: str) -> tuple[bool, List[str]]:
        """验证聚合查询配置"""
        errors = []
        
        # 验证聚合函数
        functions = aggregations.get("functions", {})
        for func_name, func_config in functions.items():
            function_type = func_config.get("function")
            if function_type not in self.aggregate_functions:
                errors.append(f"不支持的聚合函数: {function_type}")
        
        return len(errors) == 0, errors
    
    def _validate_annotations(self, annotations: Dict[str, Any], model_name: str) -> tuple[bool, List[str]]:
        """验证注解查询配置"""
        errors = []
        
        for ann_name, ann_config in annotations.items():
            function_type = ann_config.get("function")
            if function_type not in self.annotation_functions:
                errors.append(f"不支持的注解函数: {function_type}")
        
        return len(errors) == 0, errors
    
    def _validate_conditions_after_annotations(self, conditions_after: Dict[str, Any], 
                                             annotations: Dict[str, Any], 
                                             model_name: str) -> tuple[bool, List[str]]:
        """验证注解后条件"""
        errors = []
        
        # 获取注解字段名
        annotation_fields = set(annotations.keys())
        
        # 验证conditions_after_annotations中的字段必须是注解字段
        after_fields = self._extract_fields_from_conditions(conditions_after)
        for field in after_fields:
            if field not in annotation_fields:
                errors.append(f"条件字段 '{field}' 必须是已定义的注解字段")
        
        return len(errors) == 0, errors
    
    def _extract_fields_from_conditions(self, conditions: Dict[str, Any]) -> Set[str]:
        """从条件中提取所有字段名"""
        fields = set()
        
        for key, value in conditions.items():
            if key in ["AND", "OR"] and isinstance(value, list):
                for condition in value:
                    if isinstance(condition, dict):
                        fields.update(self._extract_fields_from_conditions(condition))
            elif key == "field" and isinstance(value, str):
                fields.add(value)
            elif isinstance(value, dict):
                fields.update(self._extract_fields_from_conditions(value))
        
        return fields
    
    def _validate_business_rules(self, query_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """验证业务规则"""
        warnings = []
        
        # 检查查询复杂度
        conditions = query_data.get("conditions", {})
        if self._count_conditions(conditions) > 10:
            warnings.append("查询条件过多，可能影响性能")
        
        # 检查limit设置
        limit = query_data.get("limit", 100)
        if limit > 1000:
            warnings.append("查询限制数量过大，建议使用分页")
        
        return True, warnings
    
    def _count_conditions(self, conditions: Dict[str, Any]) -> int:
        """递归计算条件数量"""
        count = 0
        for key, value in conditions.items():
            if key in ["AND", "OR"] and isinstance(value, list):
                for cond in value:
                    count += self._count_conditions(cond) if isinstance(cond, dict) else 1
            else:
                count += 1
        return count
    
    def _assess_query_complexity(self, conditions: Dict[str, Any]) -> str:
        """评估查询复杂度"""
        condition_count = self._count_conditions(conditions)
        has_logic_ops = "AND" in conditions or "OR" in conditions
        
        if condition_count == 0:
            return "无条件"
        elif condition_count == 1 and not has_logic_ops:
            return "简单"
        elif condition_count <= 3 and not has_logic_ops:
            return "中等"
        elif has_logic_ops or condition_count > 3:
            return "复杂"
        else:
            return "未知"


# 全局验证器实例
query_validator = QueryValidator()


def validate_flight_query_complete(query_data: Dict[str, Any]) -> ValidationResult:
    """
    完整验证航班查询的便捷函数
    
    Args:
        query_data: 查询条件字典
        
    Returns:
        ValidationResult: 详细验证结果
    """
    return query_validator.validate_complete(query_data)
