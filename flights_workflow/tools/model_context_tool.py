"""
模型上下文工具 - 统一获取Django模型的结构信息和样例数据

将模型内省和样例数据获取功能合并，提供一站式的模型上下文信息，
便于LLM更好地理解模型结构和实际数据特征。
"""

import os
import django
import json
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, date

# 配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from django.core.serializers.json import DjangoJSONEncoder
from django.contrib.gis.geos import GEOSGeometry
from django.contrib.gis.db.models.fields import GeometryField
from django.contrib.postgres.fields import ArrayField, JSONField
from django.db.models.fields.related import ForeignKey, ManyToManyField, OneToOneField
from django.db.models.fields.reverse_related import ManyToOneRel, ManyToManyRel, OneToOneRel

from flights.models import (
    aviation_airports, aviation_aircraft, aviation_flight
)

# 模型映射
AVAILABLE_MODELS = {
    'aviation_airports': aviation_airports,
    'aviation_aircraft': aviation_aircraft,
    'aviation_flight': aviation_flight,
}

# 大数据量表，跳过count统计
LARGE_TABLES = {'aviation_flight'}


class CustomJSONEncoder(DjangoJSONEncoder):
    """自定义JSON编码器"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, GEOSGeometry):
            return {
                'type': 'geometry',
                'geom_type': obj.geom_type,
                'wkt': str(obj)[:100] + '...' if len(str(obj)) > 100 else str(obj)
            }
        return super().default(obj)


def get_model_context(model_name: str, 
                     sample_limit: int = 5, 
                     sample_offset: int = 0,
                     sample_random: bool = False,
                     include_relationships: bool = True,
                     include_meta: bool = True,
                     exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    获取单个模型的完整上下文信息（模型结构 + 样例数据）
    
    Args:
        model_name: 模型名称
        sample_limit: 样例数据条数限制
        sample_offset: 样例数据偏移量（非随机模式）
        sample_random: 是否随机获取样例数据
        include_relationships: 是否包含关系字段信息
        include_meta: 是否包含模型元信息
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 包含模型信息和样例数据的完整上下文
    """
    if model_name not in AVAILABLE_MODELS:
        return {
            "error": f"模型 '{model_name}' 不存在",
            "available_models": list(AVAILABLE_MODELS.keys())
        }
    
    model_class = AVAILABLE_MODELS[model_name]
    
    try:
        # 获取模型结构信息
        model_info = _introspect_model_structure(
            model_class, model_name, include_relationships, include_meta, exclude_sys_fields
        )
        
        # 获取样例数据
        sample_data = _get_model_sample_data(
            model_class, model_name, sample_limit, sample_offset, 
            sample_random, exclude_sys_fields
        )
        
        # 构建统一的上下文
        return {
            "model_name": model_name,
            "context": {
                "model_info": model_info,
                "sample_data": sample_data
            }
        }
        
    except Exception as e:
        return {
            "error": f"获取模型上下文失败: {str(e)}",
            "model_name": model_name
        }


def get_models_context(model_names: List[str],
                      sample_limit: int = 5,
                      sample_offset: int = 0,
                      sample_random: bool = False,
                      include_relationships: bool = True,
                      include_meta: bool = True,
                      exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """
    批量获取多个模型的上下文信息
    
    Args:
        model_names: 模型名称列表
        sample_limit: 样例数据条数限制
        sample_offset: 样例数据偏移量（非随机模式）
        sample_random: 是否随机获取样例数据
        include_relationships: 是否包含关系字段信息
        include_meta: 是否包含模型元信息
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        Dict: 按模型名分离的上下文信息
    """
    contexts = {}
    
    for model_name in model_names:
        contexts[model_name] = get_model_context(
            model_name, sample_limit, sample_offset, sample_random,
            include_relationships, include_meta, exclude_sys_fields
        )
    
    return contexts


def _introspect_model_structure(model_class, model_name: str,
                               include_relationships: bool = True,
                               include_meta: bool = True,
                               exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """获取模型结构信息（从 introspect_models.py 提取）"""
    # 基本信息
    result = {
        "model_name": model_name,
        "verbose_name": str(model_class._meta.verbose_name),
        "fields": {},
        "primary_key": None,
        "indexes": [],
    }
    
    # 获取字段信息
    for field in model_class._meta.get_fields():
        # 过滤系统字段
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue
        
        # 跳过反向关系字段（除非明确要求包含关系）
        if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
            if not include_relationships:
                continue
            field_info = _get_reverse_relation_info(field)
        else:
            field_info = _get_field_info(field)
        
        result["fields"][field.name] = field_info
        
        # 标记主键
        if hasattr(field, 'primary_key') and field.primary_key:
            result["primary_key"] = field.name
    
    # 获取索引信息
    if include_meta:
        result["indexes"] = _get_indexes_info(model_class)
    
    return result


def _get_model_sample_data(model_class, model_name: str,
                          limit: int = 5, offset: int = 0,
                          random: bool = False,
                          exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """获取模型样例数据（从 sample_data.py 提取）"""
    # 获取总记录数
    try:
        if model_name in LARGE_TABLES:
            total_count = 9999999
        else:
            total_count = model_class.objects.count()
    except Exception as e:
        return {
            "error": f"数据库访问错误: {str(e)}",
            "table_name": model_class._meta.db_table
        }
    
    if total_count == 0:
        return {
            "total_count": 0,
            "sample_count": 0,
            "samples": [],
            "fields_summary": _get_fields_summary(model_class, exclude_sys_fields)
        }
    
    # 获取几何字段列表
    geometry_fields = [
        field.name for field in model_class._meta.get_fields()
        if isinstance(field, GeometryField)
    ]
    
    # 构建查询集
    queryset = model_class.objects.defer(*geometry_fields) if geometry_fields else model_class.objects.all()
    
    # 应用随机或顺序获取
    if random:
        queryset = queryset.order_by('?')[:limit]
    else:
        queryset = queryset[offset:offset+limit]
    
    # 转换为字典列表
    sample_records = []
    for instance in list(queryset):
        try:
            record = _model_instance_to_dict(instance, exclude_sys_fields)
            sample_records.append(record)
        except Exception as e:
            sample_records.append({
                "error": f"记录转换失败: {str(e)}",
                "pk": getattr(instance, 'pk', None)
            })
    
    result = {
        "total_count": total_count,
        "sample_count": len(sample_records),
        "samples": sample_records,
        "fields_summary": _get_fields_summary(model_class, exclude_sys_fields)
    }
    
    if random:
        result["sample_type"] = "random"
    else:
        result["offset"] = offset
        result["limit"] = limit
    
    return result


def _get_field_info(field) -> Dict[str, Any]:
    """获取字段详细信息（从 introspect_models.py 提取）"""
    field_info = {
        "type": field.__class__.__name__,
        "verbose_name": str(getattr(field, 'verbose_name', field.name)),
    }

    # 字段help_text
    if hasattr(field, 'help_text') and field.help_text:
        field_info["help_text"] = str(field.help_text)
    
    # 数字字段的范围
    if hasattr(field, 'max_digits'):
        field_info["max_digits"] = field.max_digits
    if hasattr(field, 'decimal_places'):
        field_info["decimal_places"] = field.decimal_places
    
    # 选择项
    if hasattr(field, 'choices') and field.choices:
        field_info["choices"] = list(field.choices)
    
    # 外键关系
    if isinstance(field, ForeignKey):
        field_info["related_model"] = field.related_model.__name__
        field_info["related_field"] = field.target_field.name
    
    # 多对多关系
    elif isinstance(field, ManyToManyField):
        field_info["related_model"] = field.related_model.__name__
        field_info["through"] = field.remote_field.through.__name__ if field.remote_field.through else None
    
    # 一对一关系
    elif isinstance(field, OneToOneField):
        field_info["related_model"] = field.related_model.__name__
        field_info["on_delete"] = str(field.remote_field.on_delete)
    
    # 数组字段
    elif isinstance(field, ArrayField):
        field_info["base_field"] = field.base_field.__class__.__name__
        if hasattr(field.base_field, 'max_length'):
            field_info["base_field_max_length"] = field.base_field.max_length
    
    # 几何字段
    elif isinstance(field, GeometryField):
        field_info["srid"] = getattr(field, 'srid', None)
        field_info["dim"] = getattr(field, 'dim', None)
        field_info["geography"] = getattr(field, 'geography', False)
    
    return field_info


def _get_reverse_relation_info(field) -> Dict[str, Any]:
    """获取反向关系字段信息（从 introspect_models.py 提取）"""
    return {
        "type": field.__class__.__name__,
        "related_model": field.related_model.__name__,
        "related_name": field.get_accessor_name(),
        "field_name": field.field.name,
        "is_reverse": True
    }


def _get_indexes_info(model_class) -> List[str]:
    """获取有索引的字段名列表（从 introspect_models.py 提取）"""
    indexed_fields = set()
    
    # 从模型定义的索引中获取字段
    for index in model_class._meta.indexes:
        indexed_fields.update(index.fields)
    
    # 从字段的db_index属性中获取
    for field in model_class._meta.get_fields():
        if hasattr(field, 'db_index') and field.db_index:
            indexed_fields.add(field.name)
    
    return sorted(list(indexed_fields))


def _model_instance_to_dict(instance, exclude_sys_fields: bool = True) -> Dict[str, Any]:
    """将Django模型实例转换为字典（从 sample_data.py 提取）"""
    data = {}
    
    for field in instance._meta.get_fields():
        # 过滤系统字段
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue
        
        # 跳过反向关系字段
        if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
            continue
        
        # 处理几何字段
        if isinstance(field, GeometryField):
            data[field.name] = {'type': 'geometry', 'note': '几何数据已省略'}
            continue
        
        try:
            if isinstance(field, ForeignKey):
                # 获取外键ID值
                data[field.name] = getattr(instance, field.attname, None)
            elif isinstance(field, ArrayField):
                # 处理数组字段，只取前3个元素
                field_value = getattr(instance, field.name)
                if isinstance(field_value, list) and len(field_value) > 3:
                    data[field.name] = field_value[:3]
                else:
                    data[field.name] = field_value
            else:
                data[field.name] = getattr(instance, field.name)
        except Exception as e:
            data[field.name] = f"<错误: {str(e)}>"
    
    return data


def _get_fields_summary(model_class, exclude_sys_fields: bool = True) -> Dict[str, str]:
    """获取模型字段摘要信息（从 sample_data.py 提取）"""
    fields_info = {}

    for field in model_class._meta.get_fields():
        if exclude_sys_fields and field.name.startswith('sys_'):
            continue

        # 跳过反向关系字段
        if isinstance(field, (ManyToOneRel, ManyToManyRel, OneToOneRel)):
            continue

        # 为不同字段类型提供更详细的信息
        if isinstance(field, ForeignKey):
            fields_info[field.name] = f"ForeignKey -> {field.related_model.__name__}"
        elif isinstance(field, GeometryField):
            fields_info[field.name] = f"GeometryField (SRID: {getattr(field, 'srid', 'unknown')})"
        elif isinstance(field, ArrayField):
            base_field_type = field.base_field.__class__.__name__
            fields_info[field.name] = f"ArrayField[{base_field_type}]"
        elif isinstance(field, JSONField):
            fields_info[field.name] = "JSONField"
        else:
            fields_info[field.name] = field.__class__.__name__

    return fields_info


def get_models_context_tool(model_names: str,
                           sample_limit: int = 5,
                           sample_offset: int = 2000,
                           sample_random: bool = False,
                           include_relationships: bool = True,
                           include_meta: bool = True,
                           exclude_sys_fields: bool = True) -> str:
    """
    统一获取Django模型的结构信息和样例数据的工具函数
    
    这个工具整合了 introspect_models 和 sample_data 的功能，
    一次性提供模型结构信息和样例数据，便于LLM理解模型和数据特征。
    
    Args:
        model_names: 逗号分隔的模型名称字符串
        sample_limit: 每个模型的样例数据条数限制
        sample_offset: 样例数据偏移量（非随机模式）
        sample_random: 是否随机获取样例数据
        include_relationships: 是否包含关系字段信息
        include_meta: 是否包含模型元信息
        exclude_sys_fields: 是否排除系统字段
        
    Returns:
        str: JSON格式的模型上下文信息，按模型名分离存放
    """
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]
    
    if not model_list:
        return json.dumps({
            "error": "必须指定至少一个模型名称",
            "available_models": list(AVAILABLE_MODELS.keys())
        }, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    # 批量获取上下文
    contexts = get_models_context(
        model_list, sample_limit, sample_offset, sample_random,
        include_relationships, include_meta, exclude_sys_fields
    )
    
    return json.dumps(contexts, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)


if __name__ == "__main__":
    # 测试代码
    print("=== 单个模型上下文测试 ===")
    print(get_models_context_tool("aviation_airports", sample_limit=2))
    
    print("\n=== 多个模型上下文测试 ===")
    print(get_models_context_tool("aviation_airports,aviation_aircraft", sample_limit=2))