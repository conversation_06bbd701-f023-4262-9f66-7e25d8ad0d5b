import os
import json
from typing import Any, Dict, List, Optional, TypedDict, Annotated
from dotenv import load_dotenv

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
# 配置Django设置
import django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

# 导入航班查询相关工具
from flights_workflow.tools.generate_query_schema_tool import generate_flight_query
from flights_workflow.tools.comprehensive_query_tool import comprehensive_flight_query

# 导入通用工具
from flights_workflow.tools.common_tools import (
    get_current_time,
    get_weather,
    get_date_range_for_recent_days,
    get_current_date
)

load_dotenv()

if os.getenv("GOOGLE_API_KEY") is None:
    raise ValueError("GOOGLE_API_KEY is not set")
api_key = os.getenv("GOOGLE_API_KEY")




# 核心工具链：优化为2步流程
CORE_FLIGHT_TOOLS = {
    # 新的2步核心流程（主要使用）
    "comprehensive_flight_query": comprehensive_flight_query,  # 综合验证+执行工具
    "generate_flight_query": generate_flight_query,           # Schema生成
}

# 通用工具
GENERAL_TOOLS = {
    "get_date_range_for_recent_days": get_date_range_for_recent_days,  # 日期范围
    "get_current_time": get_current_time,
    "get_weather": get_weather,
}

# 所有可用工具
AVAILABLE_TOOLS = {**CORE_FLIGHT_TOOLS, **GENERAL_TOOLS}


def get_tools_by_names(tool_names: List[str]) -> List:
    """根据工具名称获取工具列表，支持动态工具选择
    
    Args:
        tool_names: 要启用的工具名称列表
        
    Returns:
        工具对象列表
    """
    tools = []
    for name in tool_names:
        if name in AVAILABLE_TOOLS:
            tools.append(AVAILABLE_TOOLS[name])
        else:
            print(f"Warning: Tool '{name}' not found in available tools")
    return tools


def get_all_tools() -> List:
    """获取所有可用工具"""
    return list(AVAILABLE_TOOLS.values())


class ReActConfiguration(TypedDict):
    """ReAct workflow配置"""
    model_name: str
    temperature: Optional[float]
    max_tokens: Optional[int]
    enabled_tools: Optional[List[str]]  # 启用的工具列表
    enable_search: Optional[bool]  # 是否启用Gemini网络搜索


# ReAct Agent State
class AgentState(TypedDict):
    """ReAct代理的状态结构，使用LangGraph的消息状态模式"""
    messages: Annotated[List[BaseMessage], add_messages]


def call_model(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    ReAct代理节点 - 负责推理和工具调用决策
    
    功能：
    1. 使用绑定工具的Gemini LLM进行推理
    2. 决定是否需要工具调用
    3. 返回AI消息（可能包含工具调用）
    
    Args:
        state: AgentState - 包含消息历史的状态
        config: RunnableConfig - 运行时配置
        
    Returns:
        Dict[str, Any] - 包含AI响应消息的状态更新
    """
    configuration = config.get("configurable", {})
    
    # 从配置中获取参数
    model_name = configuration.get("model_name", "gemini-2.0-flash")
    temperature = configuration.get("temperature", 0.7)
    enabled_tools = configuration.get("enabled_tools", [])
    
    # 获取启用的工具
    tools = get_tools_by_names(enabled_tools) if enabled_tools else get_all_tools()
    
    # 初始化Gemini LLM
    llm = ChatGoogleGenerativeAI(
        model=model_name,
        temperature=temperature,
        google_api_key=api_key
    )
    
    # 绑定工具到模型，使LLM能够进行工具调用
    if tools:
        llm = llm.bind_tools(tools)
    
    # 准备消息，确保包含系统提示以指导推理逻辑
    messages = state["messages"]
    if not messages or not isinstance(messages[0], SystemMessage):
        system_prompt = """你是一个智能的航班查询ReAct代理，专门处理航班相关的查询需求。

核心工作流程（优化的2步链路）：
1. 推理（Reasoning）：分析用户的航班查询需求，理解查询意图
2. Schema生成：使用generate_flight_query工具将自然语言转换为标准查询Schema
3. 综合验证执行：使用comprehensive_flight_query工具进行语法验证+语义验证+查询执行

核心工具链：
- generate_flight_query: 【步骤1】将用户自然语言查询转换为符合FLIGHT_QUERY_SCHEMA格式的标准查询
- comprehensive_flight_query: 【步骤2】对生成的Schema进行综合验证并执行查询，包含：
  * 语法验证：检查Schema格式和字段存在性
  * 语义验证：使用LLM验证查询逻辑与用户意图的匹配性
  * 查询执行：执行验证通过的查询并返回结果

通用工具：
- get_date_range_for_recent_days: 获取最近N天的日期范围
- get_current_time: 获取当前时间
- get_weather: 获取天气信息

重要原则：
- 使用优化的2步流程：generate_flight_query → comprehensive_flight_query
- 综合工具提供完整的错误处理和语义验证，提升查询准确率
- 主动使用工具完成查询，不要向用户询问更多细节
- comprehensive_flight_query会自动处理验证和执行，无需额外工具

标准处理流程：
1. 用户提出航班查询需求
2. 使用generate_flight_query工具生成标准查询Schema
3. 使用comprehensive_flight_query工具进行综合验证和执行
4. 分析结果并给出用户友好的回答

示例处理方式：
- 用户："查询United Airlines的航班" → generate_flight_query → comprehensive_flight_query
- 用户："统计机场类型" → generate_flight_query（聚合查询）→ comprehensive_flight_query
- 用户："最近一周的航班" → generate_flight_query → comprehensive_flight_query

请用中文回答用户问题，严格按照2步核心链路处理航班查询。"""
        messages = [SystemMessage(content=system_prompt)] + messages
    
    # 调用LLM进行推理，返回包含推理过程和可能的工具调用的AI消息
    response = llm.invoke(messages)
    
    return {"messages": [response]}


def should_continue(state: AgentState) -> str:
    """
    条件路由函数 - 决定是否继续工具调用
    
    检查最后一条消息是否包含工具调用，实现路由逻辑：
    - 有工具调用返回"continue"
    - 无工具调用返回"end"
    
    Args:
        state: AgentState - 包含消息历史的状态
        
    Returns:
        str - "continue"表示继续执行工具，"end"表示结束workflow
    """
    messages = state["messages"]
    last_message = messages[-1]
    
    # 检查最后一条消息是否包含工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "continue"  # 有工具调用，继续执行工具
    
    # 无工具调用，结束workflow
    return "end"


def tool_node(state: AgentState) -> Dict[str, Any]:
    """执行工具调用"""
    outputs = []
    last_message = state["messages"][-1]
    
    # 创建工具名称到工具对象的映射
    tools_by_name = {tool.name: tool for tool in get_all_tools()}
    
    # 执行所有工具调用
    for tool_call in last_message.tool_calls:
        try:
            tool_result = tools_by_name[tool_call["name"]].invoke(tool_call["args"])
            outputs.append(
                ToolMessage(
                    content=json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        except Exception as e:
            outputs.append(
                ToolMessage(
                    content=f"工具执行错误: {str(e)}",
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
    
    return {"messages": outputs}


# 构建ReAct工作流
builder = StateGraph(AgentState, config_schema=ReActConfiguration)

# 添加节点
builder.add_node("agent", call_model)
builder.add_node("tools", tool_node)

# 设置入口点
builder.add_edge(START, "agent")

# 添加条件边
builder.add_conditional_edges(
    "agent",
    should_continue,
    {
        "continue": "tools",
        "end": END,
    },
)

# 工具执行后返回到代理
builder.add_edge("tools", "agent")

# 编译图
graph = builder.compile()

