#!/usr/bin/env python3
"""
测试使用新google-genai包的搜索工具
"""
import sys
import os
sys.path.append('flights_workflow')

from flights_workflow.graph import gemini_search

def test_updated_search_tool():
    """测试更新后的搜索工具功能"""
    print("=== 测试更新后的Gemini搜索工具 ===")
    
    # 检查API密钥
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("⚠️  GOOGLE_API_KEY未设置，将测试工具但不执行搜索")
    else:
        print("✅ GOOGLE_API_KEY已设置")
    
    # 获取搜索工具信息
    print(f"✅ 搜索工具导入成功: {gemini_search.name}")
    print(f"📝 工具描述: {gemini_search.description.strip()}")
    
    if api_key:
        print("\n🔍 执行测试搜索...")
        try:
            # 执行一个简单的搜索测试
            result = gemini_search.invoke({"query": "最新的LLM发布新闻"})
            print("✅ 搜索执行成功")
            print(f"📄 搜索结果长度: {len(result)} 字符")
            print(f"🔍 结果预览: {result[:300]}...")
                            
        except Exception as e:
            print(f"❌ 搜索执行失败: {e}")
    else:
        print("⏭️  跳过实际搜索测试（需要API密钥）")
    
    print("\n=== 测试完成 ===")
    print("🎉 google-genai包集成成功！")

if __name__ == "__main__":
    test_updated_search_tool()