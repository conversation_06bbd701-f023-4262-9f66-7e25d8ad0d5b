#!/usr/bin/env python3
"""
专门测试tool_node函数的单元测试
"""

import json
from flights_workflow.graph import tool_node, AgentState, get_all_tools
from langchain_core.messages import AIMessage, ToolMessage


def test_tool_node_basic_functionality():
    """测试tool_node的基本功能"""
    print("=== 测试tool_node基本功能 ===")
    
    # 模拟一个包含工具调用的AI消息
    ai_message = AIMessage(
        content="我需要获取当前时间",
        tool_calls=[
            {
                "name": "get_current_time",
                "args": {},
                "id": "call_123"
            }
        ]
    )
    
    # 创建状态
    state = AgentState(messages=[ai_message])
    
    # 调用tool_node
    result = tool_node(state)
    
    # 验证结果
    assert "messages" in result
    assert len(result["messages"]) == 1
    
    tool_message = result["messages"][0]
    assert isinstance(tool_message, ToolMessage)
    assert tool_message.name == "get_current_time"
    assert tool_message.tool_call_id == "call_123"
    assert "当前时间是:" in tool_message.content
    
    print("✅ 基本功能测试通过")


def test_tool_node_weather_call():
    """测试天气工具调用"""
    print("=== 测试天气工具调用 ===")
    
    ai_message = AIMessage(
        content="我需要获取北京的天气",
        tool_calls=[
            {
                "name": "get_weather",
                "args": {"location": "北京"},
                "id": "call_456"
            }
        ]
    )
    
    state = AgentState(messages=[ai_message])
    result = tool_node(state)
    
    tool_message = result["messages"][0]
    assert isinstance(tool_message, ToolMessage)
    assert tool_message.name == "get_weather"
    assert tool_message.tool_call_id == "call_456"
    assert "晴天，温度 15°C，微风" in tool_message.content
    
    print("✅ 天气工具调用测试通过")


def test_tool_node_multiple_calls():
    """测试多个工具调用"""
    print("=== 测试多个工具调用 ===")
    
    ai_message = AIMessage(
        content="我需要获取时间和天气",
        tool_calls=[
            {
                "name": "get_current_time",
                "args": {},
                "id": "call_time"
            },
            {
                "name": "get_weather",
                "args": {"location": "上海"},
                "id": "call_weather"
            }
        ]
    )
    
    state = AgentState(messages=[ai_message])
    result = tool_node(state)
    
    # 验证返回了两个工具消息
    assert len(result["messages"]) == 2
    
    # 验证第一个工具调用
    time_message = result["messages"][0]
    assert isinstance(time_message, ToolMessage)
    assert time_message.name == "get_current_time"
    assert time_message.tool_call_id == "call_time"
    
    # 验证第二个工具调用
    weather_message = result["messages"][1]
    assert isinstance(weather_message, ToolMessage)
    assert weather_message.name == "get_weather"
    assert weather_message.tool_call_id == "call_weather"
    assert "多云，温度 18°C，东南风" in weather_message.content
    
    print("✅ 多个工具调用测试通过")


def test_tool_node_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    # 模拟一个不存在的工具调用
    ai_message = AIMessage(
        content="调用不存在的工具",
        tool_calls=[
            {
                "name": "nonexistent_tool",
                "args": {},
                "id": "call_error"
            }
        ]
    )
    
    state = AgentState(messages=[ai_message])
    result = tool_node(state)
    
    # 验证错误处理
    tool_message = result["messages"][0]
    assert isinstance(tool_message, ToolMessage)
    assert tool_message.name == "nonexistent_tool"
    assert tool_message.tool_call_id == "call_error"
    assert "工具执行错误:" in tool_message.content
    
    print("✅ 错误处理测试通过")


def test_tool_node_invalid_args():
    """测试无效参数处理"""
    print("=== 测试无效参数处理 ===")
    
    # 模拟天气工具调用但缺少必需参数
    ai_message = AIMessage(
        content="获取天气但参数错误",
        tool_calls=[
            {
                "name": "get_weather",
                "args": {},  # 缺少location参数
                "id": "call_invalid"
            }
        ]
    )
    
    state = AgentState(messages=[ai_message])
    result = tool_node(state)
    
    # 验证错误处理
    tool_message = result["messages"][0]
    assert isinstance(tool_message, ToolMessage)
    assert tool_message.name == "get_weather"
    assert tool_message.tool_call_id == "call_invalid"
    assert "工具执行错误:" in tool_message.content
    
    print("✅ 无效参数处理测试通过")


if __name__ == "__main__":
    print("开始测试tool_node函数...")
    
    test_tool_node_basic_functionality()
    test_tool_node_weather_call()
    test_tool_node_multiple_calls()
    test_tool_node_error_handling()
    test_tool_node_invalid_args()
    
    print("\n🎉 所有tool_node测试通过！")