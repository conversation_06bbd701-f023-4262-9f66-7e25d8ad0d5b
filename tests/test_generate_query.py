# 测试多模型支持
import sys
sys.path.append('.')
import json
from flights_workflow.tools.model_context_tool import get_models_context, CustomJSONEncoder

try:
    # 测试多个模型
    model_names = 'aviation_flight,aviation_airports,aviation_aircraft'
    model_list = [name.strip() for name in model_names.split(',') if name.strip()]

    models_context = get_models_context(
        model_list,
        sample_limit=3,
        sample_offset=2000,
        sample_random=False,
        include_relationships=True,
        include_meta=True,
        exclude_sys_fields=True
    )

    # 测试JSON序列化
    test_data = {'models_context': models_context}
    result = json.dumps(test_data, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    parsed = json.loads(result)

    print('=== Multi-Model Test ===')
    print(f'Models loaded: {sorted(parsed["models_context"].keys())}')

    for model_name in ['aviation_flight', 'aviation_airports', 'aviation_aircraft']:
        if model_name in parsed['models_context']:
            model_data = parsed['models_context'][model_name]
            if 'error' not in model_data:
                print(model_data)
                # field_count = len(model_data['context']['model_info']['fields'])
                # sample_count = model_data['context']['sample_data']['sample_count']
                # print(f'✅ {model_name}: {field_count} fields, {sample_count} samples')
            else:
                print(f'❌ {model_name}: {model_data["error"]}')

    print('✅ Multi-model support works correctly!')

except Exception as e:
    print(f'Test failed: {str(e)}')
    import traceback
    traceback.print_exc()