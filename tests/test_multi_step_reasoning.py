#!/usr/bin/env python3
"""
ReAct多步推理能力专项测试

专门测试ReAct workflow的多步推理、条件判断和复杂决策能力
验证推理-行动-观察循环在复杂场景下的表现，并打印详细的对话过程
"""

import time
from flights_workflow.graph import react_graph, ReActConfiguration
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig


def print_message_details(messages):
    """打印消息详情，展示推理过程"""
    print("\n📋 详细对话过程:")
    print("-" * 50)
    
    for i, message in enumerate(messages, 1):
        message_type = type(message).__name__
        
        if hasattr(message, 'content') and message.content:
            print(f"{i}. [{message_type}] {message.content}")
        
        # 如果是AI消息且有工具调用
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                print(f"   🔧 工具调用: {tool_call['name']}({tool_call.get('args', {})})")
        
        # 如果是工具消息
        if message_type == 'ToolMessage':
            print(f"   📊 工具结果: {message.content}")
        
        print()


def test_conditional_reasoning_with_details():
    """测试条件推理能力并打印详细过程"""
    print("=" * 70)
    print("  🧠 多步推理详细测试")
    print("=" * 70)
    
    query = "现在几点？如果是下午，告诉我北京的天气"
    print(f"🧪 测试查询: {query}")
    print(f"🎯 预期推理步骤: 1. 获取时间 → 2. 判断是否下午 → 3. 如果是下午则获取天气")
    
    # 准备配置
    config = ReActConfiguration(
        model_name="gemini-2.0-flash",
        temperature=0.3,
        enabled_tools=["get_current_time", "get_weather"],
        enable_search=False
    )
    
    # 准备初始状态
    initial_state = {
        "messages": [HumanMessage(content=query)]
    }
    
    # 准备运行配置
    run_config = RunnableConfig(configurable=config)
    
    print("\n🚀 开始执行ReAct workflow...")
    start_time = time.time()
    
    try:
        # 调用ReAct workflow
        result = react_graph.invoke(initial_state, config=run_config)
        end_time = time.time()
        
        # 打印详细的消息过程
        print_message_details(result["messages"])
        
        # 提取最终回答
        final_message = result["messages"][-1]
        print("=" * 50)
        print(f"🎉 最终回答: {final_message.content}")
        print(f"⏱️ 总推理时间: {end_time - start_time:.2f}秒")
        print(f"📊 总消息数: {len(result['messages'])}")
        
        # 分析推理过程
        print("\n🔍 推理过程分析:")
        tool_calls_count = 0
        tools_used = []
        
        for message in result["messages"]:
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_calls_count += 1
                    tools_used.append(tool_call['name'])
        
        print(f"   - 工具调用次数: {tool_calls_count}")
        print(f"   - 使用的工具: {', '.join(set(tools_used))}")
        print(f"   - 推理步骤数: {len([m for m in result['messages'] if type(m).__name__ == 'AIMessage'])}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_simple_reasoning_test():
    """运行简化的多步推理测试"""
    print("🧠 ReAct多步推理详细测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        success = test_conditional_reasoning_with_details()
        
        if success:
            print("\n" + "=" * 70)
            print("  ✅ 测试总结")
            print("=" * 70)
            print("🎯 成功验证了ReAct的多步推理能力:")
            print("   - 条件判断逻辑 ✓")
            print("   - 工具调用决策 ✓")
            print("   - 推理-行动-观察循环 ✓")
            print("   - 信息整合能力 ✓")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_simple_reasoning_test()
    if success:
        print(f"\n🎉 ReAct多步推理测试成功完成！")
        print("💡 系统展现了良好的推理-行动-观察循环能力")
    else:
        print(f"\n💥 多步推理测试过程中出现问题")
        exit(1)