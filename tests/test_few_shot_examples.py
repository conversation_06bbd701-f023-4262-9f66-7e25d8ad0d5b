"""
Few-Shot示例语法验证测试

验证generate_query_schema_tool中的few-shot示例是否符合语法规范，
使用comprehensive_query_tool中的本地语法验证函数进行检查。
"""

import json
import sys
import os
import unittest
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flights_workflow.tools.generate_query_schema_tool import get_few_shot_examples
from flights_workflow.tools.query_validator import validate_flight_query_complete


class TestFewShotExamples(unittest.TestCase):
    """测试Few-Shot示例的语法正确性"""
    
    def setUp(self):
        """获取所有few-shot示例"""
        self.examples = get_few_shot_examples()
        self.assertIsInstance(self.examples, list)
        self.assertGreater(len(self.examples), 0)
    
    def test_all_examples_syntax_validation(self):
        """测试所有few-shot示例的语法验证"""
        validation_results = []
        
        for i, example in enumerate(self.examples):
            with self.subTest(example_index=i, user_query=example.get("user_query", "Unknown")):
                # 提取生成的查询
                generated_query = example.get("generated_query", {})
                
                # 使用新的统一验证器进行完整验证
                validation_result_obj = validate_flight_query_complete(generated_query)
                
                validation_result = {
                    "valid": validation_result_obj.valid,
                    "error": "; ".join(validation_result_obj.errors) if validation_result_obj.errors else None,
                    "type": "complete_validation",
                    "warnings": validation_result_obj.warnings,
                    "complexity": validation_result_obj.query_complexity
                }
                
                # 记录验证结果
                validation_results.append({
                    "index": i,
                    "user_query": example.get("user_query", "Unknown"),
                    "validation": validation_result,
                    "query": generated_query
                })
                
                # 断言Schema验证通过
                self.assertTrue(
                    validation_result["valid"], 
                    f"示例 {i} Schema验证失败: {validation_result.get('error', 'Unknown error')}\n"
                    f"用户查询: {example.get('user_query', 'Unknown')}\n"
                    f"查询: {json.dumps(generated_query, ensure_ascii=False, indent=2)}"
                )
        
        # 打印验证摘要
        self._print_validation_summary(validation_results)
    
    def test_individual_examples(self):
        """逐个测试每个示例的详细验证"""
        expected_examples = [
            {
                "name": "东方航空查询",
                "model": "aviation_flight",
                "has_conditions": True,
                "has_aggregations": False,
                "has_annotations": False
            },
            {
                "name": "今天航班查询", 
                "model": "aviation_flight",
                "has_conditions": True,
                "has_aggregations": False,
                "has_annotations": False
            },
            {
                "name": "航空公司航班统计",
                "model": "aviation_flight", 
                "has_conditions": False,
                "has_aggregations": True,
                "has_annotations": False
            },
            {
                "name": "中国国际机场查询",
                "model": "aviation_airports",
                "has_conditions": True,
                "has_aggregations": False,
                "has_annotations": False
            },
            {
                "name": "波音737查询",
                "model": "aviation_aircraft",
                "has_conditions": True,
                "has_aggregations": False,
                "has_annotations": False
            },
            {
                "name": "延误航班查询",
                "model": "aviation_flight",
                "has_conditions": True,
                "has_aggregations": False,
                "has_annotations": True
            },
            {
                "name": "机场航班数量统计",
                "model": "aviation_flight",
                "has_conditions": False,
                "has_aggregations": True,
                "has_annotations": False
            }
        ]
        
        for i, (example, expected) in enumerate(zip(self.examples, expected_examples)):
            with self.subTest(example_name=expected["name"]):
                query = example["generated_query"]
                
                # 验证模型
                self.assertEqual(query["model"], expected["model"])
                
                # 验证结构特征
                self.assertEqual(
                    "conditions" in query and bool(query.get("conditions")),
                    expected["has_conditions"],
                    f"{expected['name']}: conditions字段不符合预期"
                )
                
                self.assertEqual(
                    "aggregations" in query,
                    expected["has_aggregations"],
                    f"{expected['name']}: aggregations字段不符合预期"
                )
                
                self.assertEqual(
                    "annotations" in query,
                    expected["has_annotations"],
                    f"{expected['name']}: annotations字段不符合预期"
                )
    
    def test_query_structure_completeness(self):
        """测试查询结构的完整性"""
        for i, example in enumerate(self.examples):
            with self.subTest(example_index=i):
                query = example["generated_query"]
                
                # 必须包含model字段
                self.assertIn("model", query, f"示例 {i} 缺少model字段")
                self.assertIn(query["model"], ["aviation_flight", "aviation_airports", "aviation_aircraft"])
                
                # 如果有conditions，必须是dict或空dict
                if "conditions" in query:
                    self.assertIsInstance(query["conditions"], dict)
                
                # 如果有aggregations，必须包含group_by和functions
                if "aggregations" in query:
                    agg = query["aggregations"]
                    self.assertIsInstance(agg, dict)
                    if "group_by" in agg:
                        self.assertIsInstance(agg["group_by"], list)
                    if "functions" in agg:
                        self.assertIsInstance(agg["functions"], dict)
                
                # 如果有annotations，必须是dict
                if "annotations" in query:
                    self.assertIsInstance(query["annotations"], dict)
                
                # 如果有ordering，必须是list
                if "ordering" in query:
                    self.assertIsInstance(query["ordering"], list)
                
                # 如果有limit，必须是int
                if "limit" in query:
                    self.assertIsInstance(query["limit"], int)
                    self.assertGreater(query["limit"], 0)
    
    def test_delay_query_time_diff_annotation(self):
        """专门测试延误查询的time_diff注解功能"""
        delay_examples = [ex for ex in self.examples if "延误" in ex.get("user_query", "")]
        self.assertGreater(len(delay_examples), 0, "应该包含延误查询示例")
        
        for example in delay_examples:
            query = example["generated_query"]
            
            # 验证annotations结构
            self.assertIn("annotations", query)
            annotations = query["annotations"]
            
            # 验证delay_minutes注解
            self.assertIn("delay_minutes", annotations)
            delay_annotation = annotations["delay_minutes"]
            
            # 验证time_diff函数配置
            self.assertEqual(delay_annotation["function"], "time_diff")
            self.assertIn("field1", delay_annotation)
            self.assertIn("field2", delay_annotation)
            self.assertIn("unit", delay_annotation)
            self.assertEqual(delay_annotation["unit"], "minutes")
            
            # 验证conditions_after_annotations
            self.assertIn("conditions_after_annotations", query)
            after_conditions = query["conditions_after_annotations"]
            self.assertIsInstance(after_conditions, dict)
    
    def _print_validation_summary(self, results: List[Dict[str, Any]]):
        """打印验证摘要"""
        print(f"\n=== Few-Shot示例语法验证摘要 ===")
        print(f"总计示例数量: {len(results)}")
        
        valid_count = sum(1 for r in results if r["validation"]["valid"])
        invalid_count = len(results) - valid_count
        
        print(f"验证通过: {valid_count}")
        print(f"验证失败: {invalid_count}")
        
        if invalid_count > 0:
            print(f"\n--- 失败的示例 ---")
            for result in results:
                if not result["validation"]["valid"]:
                    print(f"示例 {result['index']}: {result['user_query']}")
                    print(f"  错误: {result['validation']['error']}")
                    print(f"  建议: {result['validation'].get('suggestions', [])}")
        
        print("=" * 40)


if __name__ == "__main__":
    # 设置详细的测试输出
    unittest.main(verbosity=2, buffer=True)