#!/usr/bin/env python3
"""
测试优化后的ReAct工作流
"""

from flights_workflow.graph import graph, AgentState
from langchain_core.messages import HumanMessage

def test_basic_conversation():
    """测试基本对话功能"""
    print("=== 测试基本对话 ===")
    
    # 配置
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.7,
            "enabled_tools": ["get_current_time", "get_weather"]
        }
    }
    
    # 输入消息
    initial_state = {
        "messages": [HumanMessage(content="你好，请告诉我现在几点了？")]
    }
    
    try:
        # 调用图
        result = graph.invoke(initial_state, config=config)
        
        print("对话结果:")
        for message in result["messages"]:
            print(f"- {message.__class__.__name__}: {message.content}")
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_weather_query():
    """测试天气查询功能"""
    print("\n=== 测试天气查询 ===")
    
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash", 
            "temperature": 0.7,
            "enabled_tools": ["get_weather", "get_current_time"]
        }
    }
    
    initial_state = {
        "messages": [HumanMessage(content="北京今天天气怎么样？")]
    }
    
    try:
        result = graph.invoke(initial_state, config=config)
        
        print("天气查询结果:")
        for message in result["messages"]:
            print(f"- {message.__class__.__name__}: {message.content}")
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    
    config = {
        "configurable": {
            "model_name": "gemini-2.0-flash",
            "temperature": 0.7,
            "enabled_tools": ["gemini_search"]
        }
    }
    
    initial_state = {
        "messages": [HumanMessage(content="搜索一下最新的AI技术发展")]
    }
    
    try:
        result = graph.invoke(initial_state, config=config)
        
        print("搜索结果:")
        for message in result["messages"]:
            print(f"- {message.__class__.__name__}: {message.content[:200]}...")
            
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    print("开始测试优化后的ReAct工作流...")
    
    test_basic_conversation()
    test_weather_query()
    test_search_functionality()
    
    print("\n测试完成！")