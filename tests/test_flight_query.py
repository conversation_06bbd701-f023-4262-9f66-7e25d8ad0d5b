#!/usr/bin/env python3
"""
测试航班查询工作流
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置Django设置
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from flights_workflow.graph import graph
from langchain_core.messages import HumanMessage
import time


def print_step_info(step_name, description=""):
    """打印步骤信息"""
    print(f"\n🔄 [{time.strftime('%H:%M:%S')}] {step_name}")
    if description:
        print(f"   {description}")
    print("-" * 50)


def test_single_query(query, query_name, log_file):
    """测试单个查询"""
    print("\n" + "=" * 80)
    print(f"🚀 开始测试: {query_name}")
    print("=" * 80)
    print(f"📝 用户提问: {query}")
    print("=" * 80)
    
    # 记录到日志文件
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"\n{'='*60}\n")
        f.write(f"测试: {query_name}\n")
        f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"{'='*60}\n")
        f.write(f"用户: {query}\n\n")
    
    # 配置
    config = {
        "configurable": {
            "model_name": "gemini-2.5-flash",
            "temperature": 0.3,
            "enabled_tools": [
                "introspect_models",
                "sample_data", 
                "generate_flight_query",
                "validate_flight_query_schema",
                "execute_flight_query",
                "get_date_range_for_recent_days",
                "gemini_search"
            ]
        }
    }
    
    # 构建初始状态
    initial_state = {
        "messages": [HumanMessage(content=query)]
    }
    
    conversation_log = []
    
    try:
        print_step_info("初始化工作流", "准备执行航班查询工作流")
        
        # 使用stream方法实时获取执行过程
        step_count = 0
        for step in graph.stream(initial_state, config):
            step_count += 1
            print_step_info(f"执行步骤 {step_count}", f"节点: {list(step.keys())}")
            
            # 打印每个节点的详细信息
            for node_name, node_result in step.items():
                print(f"📍 节点: {node_name}")
                
                if "messages" in node_result:
                    messages = node_result["messages"]
                    for i, message in enumerate(messages):
                        print(f"  💬 消息 {i+1} ({message.__class__.__name__}):")
                        
                        if hasattr(message, 'content') and message.content:
                            content = str(message.content)
                            
                            # 记录AI的回复到对话日志
                            if message.__class__.__name__ == "AIMessage":
                                if hasattr(message, 'tool_calls') and message.tool_calls:
                                    # 有工具调用的消息
                                    tool_names = [tc.get('name', 'unknown') for tc in message.tool_calls]
                                    conversation_log.append(f"AI (调用工具 {', '.join(tool_names)}): {content}")
                                else:
                                    # 最终回复
                                    conversation_log.append(f"AI: {content}")
                            elif message.__class__.__name__ == "ToolMessage":
                                tool_name = getattr(message, 'name', 'unknown')
                                conversation_log.append(f"工具 {tool_name}: {content[:200]}...")
                            
                            # 限制内容长度以便阅读
                            if len(content) > 500:
                                content = content[:500] + "..."
                            print(f"     内容: {content}")
                        
                        if hasattr(message, 'tool_calls') and message.tool_calls:
                            print(f"     🔧 工具调用: {len(message.tool_calls)} 个")
                            for j, tool_call in enumerate(message.tool_calls):
                                print(f"       {j+1}. {tool_call.get('name', 'unknown')}")
                                if 'args' in tool_call:
                                    args_str = str(tool_call['args'])
                                    if len(args_str) > 100:
                                        args_str = args_str[:100] + "..."
                                    print(f"          参数: {args_str}")
                
                print()
        
        print("\n" + "=" * 20)
        print(f"✅ {query_name} 执行完成!")
        print("=" * 20)
        
        # 保存对话到日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            for log_entry in conversation_log:
                f.write(f"{log_entry}\n")
            f.write(f"\n状态: 执行成功\n")
        
    except Exception as e:
        print(f"\n❌ 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 记录错误到日志
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"错误: {str(e)}\n")
            f.write(f"状态: 执行失败\n")


def test_flight_queries():
    """测试多个航班查询"""
    
    # 创建简单的日志文件
    log_file = f"logs/conversation_{time.strftime('%Y%m%d_%H%M%S')}.txt"
    
    # 定义测试用例
    test_cases = [
        # {
        #     "query": "帮我查询从北京到上海的航班信息，我想要明天出发的",
        #     "name": "北京到上海航班查询"
        # },
        # {
        #     "query": "世界上有多少个机场？按机场类型（大、中、小）分别统计数量",
        #     "name": "全球机场统计查询"
        # },
        # {
        #     "query": "最近2个月美国、俄罗斯政府飞机都去了哪些国家？说明什么",
        #     "name": "美国和俄罗斯航班情况"
        # },
        {
            "query": "按出发日期统计最近1个月北京飞上海的航班趋势情况",
            "name": "北京到上海航班查询"
        },
        
    ]
    
    print("=" * 30)
    print("航班查询系统测试")
    print("=" * 30)
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔢 测试用例数量: {len(test_cases)}")
    print(f"📝 对话日志: {log_file}")
    print("=" * 30)
    
    # 创建日志文件并写入头部信息
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write("航班查询系统对话日志\n")
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试用例数量: {len(test_cases)}\n")
        f.write("="*60 + "\n")
    
    # 逐个执行测试用例
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*10} 测试用例 {i}/{len(test_cases)} {'='*10}")
        test_single_query(test_case["query"], test_case["name"], log_file)
        
        # 在测试用例之间添加间隔
        if i < len(test_cases):
            print(f"\n⏳ 等待 2 秒后开始下一个测试...")
            time.sleep(2)
    
    print("\n" + "=" * 30)
    print("所有测试完成!")
    print(f"📝 完整对话记录已保存到: {log_file}")
    print("=" * 30)


if __name__ == "__main__":
    test_flight_queries()