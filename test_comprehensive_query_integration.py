#!/usr/bin/env python3
"""
测试 comprehensive_flight_query 工具与 graph.py 的集成
使用 get_few_shot_examples 中的真实示例数据进行测试
"""

import os
import sys
import json
import django
from typing import Dict, Any, List

# 配置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

# 导入必要的模块
from flights_workflow.tools.generate_query_schema_tool import get_few_shot_examples, generate_flight_query
from flights_workflow.tools.comprehensive_query_tool import comprehensive_flight_query
from flights_workflow.graph import graph, ReActConfiguration


def test_few_shot_examples():
    """测试从 get_few_shot_examples 获取的示例数据"""
    print("=" * 60)
    print("测试 get_few_shot_examples 函数")
    print("=" * 60)
    
    examples = get_few_shot_examples()
    print(f"获取到 {len(examples)} 个示例:")
    
    for i, example in enumerate(examples, 1):
        print(f"\n示例 {i}:")
        print(f"  用户查询: {example['user_query']}")
        print(f"  生成查询: {json.dumps(example['generated_query'], ensure_ascii=False, indent=4)}")
    
    return examples


def test_comprehensive_query_with_examples(examples: List[Dict[str, Any]]):
    """使用示例数据测试 comprehensive_flight_query 工具"""
    print("\n" + "=" * 60)
    print("测试 comprehensive_flight_query 工具")
    print("=" * 60)
    
    results = []
    
    for i, example in enumerate(examples, 1):
        print(f"\n测试示例 {i}: {example['user_query']}")
        print("-" * 40)
        
        # 将生成的查询转换为JSON字符串
        query_schema = json.dumps(example['generated_query'], ensure_ascii=False)
        user_question = example['user_query']
        
        try:
            # 调用 comprehensive_flight_query 工具 - 使用 invoke 方法
            result = comprehensive_flight_query.invoke({
                "query_schema": query_schema,
                "user_question": user_question
            })
            
            # 解析结果
            result_dict = json.loads(result)
            
            print(f"✅ 查询成功")
            print(f"查询类型: {result_dict.get('query_type', 'unknown')}")
            print(f"结果数量: {result_dict.get('count', 0)}")
            
            # 显示部分结果
            if result_dict.get('success') and result_dict.get('result'):
                result_data = result_dict['result']
                if isinstance(result_data, list) and len(result_data) > 0:
                    print(f"示例结果: {json.dumps(result_data[0], ensure_ascii=False, indent=2)}")
                elif isinstance(result_data, dict):
                    print(f"聚合结果: {json.dumps(result_data, ensure_ascii=False, indent=2)}")
            
            results.append({
                'example': example,
                'success': True,
                'result': result_dict
            })
            
        except Exception as e:
            print(f"❌ 查询失败: {str(e)}")
            results.append({
                'example': example,
                'success': False,
                'error': str(e)
            })
    
    return results


def test_graph_integration():
    """测试与 graph.py 的集成"""
    print("\n" + "=" * 60)
    print("测试与 graph.py 的集成")
    print("=" * 60)
    
    # 获取示例数据
    examples = get_few_shot_examples()
    
    # 选择几个代表性的查询进行测试
    test_queries = [
        "查询东方航空的航班",
        "统计各航空公司的航班数量", 
        "查询中国的国际机场"
    ]
    
    for query in test_queries:
        print(f"\n测试查询: {query}")
        print("-" * 40)
        
        try:
            # 配置
            config = {
                "configurable": {
                    "model_name": "gemini-2.0-flash",
                    "temperature": 0.7,
                    "enabled_tools": ["generate_flight_query", "comprehensive_flight_query"]
                }
            }
            
            # 构建输入
            input_data = {
                "messages": [{"role": "user", "content": query}]
            }
            
            # 执行图
            result = graph.invoke(input_data, config)
            
            # 显示结果
            if result and "messages" in result:
                last_message = result["messages"][-1]
                if hasattr(last_message, 'content'):
                    print(f"✅ 图执行成功")
                    print(f"响应: {last_message.content[:200]}...")
                else:
                    print(f"✅ 图执行成功，消息类型: {type(last_message)}")
            else:
                print(f"⚠️ 图执行完成，但结果格式异常")
                
        except Exception as e:
            print(f"❌ 图执行失败: {str(e)}")


def generate_test_report(test_results: List[Dict[str, Any]]):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("测试报告")
    print("=" * 60)
    
    total_tests = len(test_results)
    successful_tests = sum(1 for r in test_results if r['success'])
    failed_tests = total_tests - successful_tests
    
    print(f"总测试数: {total_tests}")
    print(f"成功: {successful_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    if failed_tests > 0:
        print(f"\n失败的测试:")
        for i, result in enumerate(test_results):
            if not result['success']:
                print(f"  {i+1}. {result['example']['user_query']}")
                print(f"     错误: {result['error']}")


def main():
    """主测试函数"""
    print("开始测试 comprehensive_flight_query 工具集成")
    
    try:
        # 1. 测试 get_few_shot_examples
        examples = test_few_shot_examples()
        
        # 2. 测试 comprehensive_flight_query 工具
        test_results = test_comprehensive_query_with_examples(examples)
        
        # 3. 测试图集成
        test_graph_integration()
        
        # 4. 生成报告
        generate_test_report(test_results)
        
        print(f"\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
