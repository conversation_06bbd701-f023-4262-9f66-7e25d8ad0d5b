# Design Document

## Overview

本设计文档描述了如何为现有的flights_qa_system项目集成基础的ReAct（Reasoning and Acting）模式workflow。设计将基于LangGraph的StateGraph架构，实现推理-行动-观察的循环处理能力。

ReAct模式的核心思想：
1. **推理（Reasoning）**: 分析用户查询，判断是否需要工具调用
2. **行动（Acting）**: 选择并执行合适的工具
3. **观察（Observing）**: 分析工具返回的结果，决定下一步行动

## Architecture

### 整体架构图

```mermaid
graph TD
    A[用户查询] --> B[ReAct Agent Node]
    B --> C{需要工具调用?}
    C -->|是| D[Tool Node]
    C -->|否| E[直接回答]
    D --> F[工具执行结果]
    F --> B
    E --> G[最终回答]
    B --> G
```

## Components and Interfaces

### 1. 状态管理 (AgentState)

基于LangGraph的消息状态模式：

```python
from typing import Annotated, List
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

class AgentState(TypedDict):
    """ReAct代理的状态结构"""
    messages: Annotated[List[BaseMessage], add_messages]
```

### 2. ReAct Agent Node

核心的代理节点，负责推理和决策：

```python
def call_model(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    ReAct代理节点 - 负责推理和工具调用决策
    
    功能：
    1. 使用绑定工具的Gemini LLM进行推理
    2. 决定是否需要工具调用
    3. 返回AI消息（可能包含工具调用）
    """
    pass
```

### 3. Tool Node

工具执行节点，处理所有工具调用：

```python
def tool_node(state: AgentState) -> Dict[str, Any]:
    """
    工具执行节点 - 处理工具调用
    
    功能：
    1. 解析最后一条消息中的工具调用
    2. 执行相应的工具
    3. 将结果包装为ToolMessage
    """
    pass
```

### 4. 条件路由函数

决定workflow的下一步：

```python
def should_continue(state: AgentState) -> str:
    """
    条件路由函数 - 决定是否继续工具调用
    
    返回值：
    - "continue": 继续执行工具
    - "end": 结束workflow
    """
    pass
```

### 5. 基础工具定义

简单的工具集合，用于验证ReAct能力：

```python
@tool
def get_current_time() -> str:
    """获取当前时间"""
    pass

@tool
def get_weather(location: str) -> str:
    """获取天气信息"""
    pass
```

## Data Models

### 消息流结构

```python
# 输入消息
HumanMessage(content="用户查询")

# AI推理消息（可能包含工具调用）
AIMessage(
    content="推理过程描述",
    tool_calls=[
        {
            "name": "tool_name",
            "args": {"param": "value"},
            "id": "call_id"
        }
    ]
)

# 工具执行结果消息
ToolMessage(
    content="工具执行结果",
    tool_call_id="call_id",
    name="tool_name"
)
```

## Error Handling

### 基础错误处理

```python
def handle_tool_error(error: Exception, tool_name: str) -> ToolMessage:
    """
    处理工具调用错误
    
    策略：
    1. 记录错误详情
    2. 返回友好的错误消息
    """
    pass
```

## Implementation Details

### StateGraph构建

```python
from langgraph.graph import StateGraph, START, END

# 创建StateGraph
workflow = StateGraph(AgentState)

# 添加节点
workflow.add_node("agent", call_model)
workflow.add_node("tools", tool_node)

# 设置入口点
workflow.set_entry_point("agent")

# 添加条件边
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {
        "continue": "tools",
        "end": END,
    },
)

# 添加普通边
workflow.add_edge("tools", "agent")

# 编译图
react_graph = workflow.compile()
```