# 需求文档

## 介绍

本项目旨在构建一个航班查询问答系统，通过将复杂的数据库查询任务分解为两个步骤来提高LLM生成查询的稳定性和准确性。第一步让LLM生成标准化的JSON格式查询条件，第二步使用本地工具将JSON条件转换为实际的数据库queryset并执行查询。

## 需求

### 需求 1：结构化查询生成

**用户故事：** 作为系统用户，我希望LLM能够根据我的自然语言问题生成标准化的JSON格式查询条件，以便系统能够准确理解我的查询意图。

#### 验收标准

1. WHEN 用户输入自然语言航班查询问题 THEN 系统 SHALL 向LLM提供预定义的JSON Schema格式规范
2. WHEN LLM接收到查询问题和Schema规范 THEN LLM SHALL 生成符合JSON Schema格式的结构化查询条件
3. WHEN LLM生成查询条件 THEN 系统 SHALL 验证输出格式的正确性和可解析性
4. IF JSON格式验证失败 THEN 系统 SHALL 提示LLM重新生成或返回错误信息

### 需求 2：查询解析和执行

**用户故事：** 作为系统用户，我希望系统能够将标准化的JSON查询条件转换为实际的数据库查询并返回准确的结果，以便我能获得所需的航班信息。

#### 验收标准

1. WHEN 系统接收到有效的JSON格式查询条件 THEN 系统 SHALL 使用本地工具解析JSON条件
2. WHEN JSON条件被解析 THEN 系统 SHALL 将解析结果转换为Django ORM queryset
3. WHEN queryset构建完成 THEN 系统 SHALL 执行数据库查询并获取结果
4. WHEN 查询执行完成 THEN 系统 SHALL 将结果格式化并返回给LLM进行最终回答生成
5. IF 查询执行失败 THEN 系统 SHALL 返回详细的错误信息和建议

### 需求 3：航班数据模型支持

**用户故事：** 作为系统用户，我希望系统能够支持对航班、机场、飞机等相关数据的查询，以便我能获得全面的航班信息。

#### 验收标准

1. WHEN 用户查询航班信息 THEN 系统 SHALL 支持aviation_flight模型的所有字段查询
2. WHEN 用户查询机场信息 THEN 系统 SHALL 支持aviation_airports模型的所有字段查询
3. WHEN 用户查询飞机信息 THEN 系统 SHALL 支持aviation_aircraft模型的所有字段查询
4. WHEN 用户进行关联查询 THEN 系统 SHALL 支持跨模型的外键关系查询
5. WHEN 用户查询地理位置相关信息 THEN 系统 SHALL 支持GeometryField字段的空间查询

### 需求 4：JSON Schema设计

**用户故事：** 作为系统开发者，我希望有一个完善的JSON Schema来规范查询条件的格式，以便确保LLM生成的查询条件具有一致性和可解析性。

#### 验收标准

1. WHEN 设计JSON Schema THEN Schema SHALL 包含所有支持的查询操作类型（等于、包含、范围、空值检查等）
2. WHEN 设计JSON Schema THEN Schema SHALL 包含所有支持的数据模型字段映射
3. WHEN 设计JSON Schema THEN Schema SHALL 包含查询条件的逻辑组合（AND、OR）
4. WHEN 设计JSON Schema THEN Schema SHALL 包含排序、分页、限制等查询选项
5. WHEN 设计JSON Schema THEN Schema SHALL 提供清晰的字段描述和示例

### 需求 5：工作流集成

**用户故事：** 作为系统用户，我希望新的查询系统能够无缝集成到现有的ReAct工作流中，以便我能通过自然语言对话获得航班查询服务。

#### 验收标准

1. WHEN 系统集成到ReAct工作流 THEN 系统 SHALL 作为新的工具节点添加到现有graph中
2. WHEN 用户在对话中提出航班查询 THEN LLM SHALL 能够识别并调用航班查询工具
3. WHEN 航班查询工具被调用 THEN 工具 SHALL 执行两步骤查询流程并返回结果
4. WHEN 查询结果返回 THEN LLM SHALL 基于结果生成自然语言回答
5. WHEN 查询过程中出现错误 THEN 系统 SHALL 提供友好的错误提示和建议

### 需求 6：查询性能和错误处理

**用户故事：** 作为系统用户，我希望查询系统具有良好的性能和健壮的错误处理机制，以便获得稳定可靠的查询体验。

#### 验收标准

1. WHEN 执行数据库查询 THEN 系统 SHALL 在合理时间内（<5秒）返回结果
2. WHEN 查询结果过多 THEN 系统 SHALL 实施分页机制限制单次返回数量
3. WHEN 发生数据库连接错误 THEN 系统 SHALL 返回明确的错误信息
4. WHEN JSON解析失败 THEN 系统 SHALL 提供具体的格式错误说明
5. WHEN 查询条件无效 THEN 系统 SHALL 提供字段验证错误详情