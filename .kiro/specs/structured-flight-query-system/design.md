# 设计文档

## 概述

本设计文档描述了结构化航班查询系统的技术架构和实现方案。该系统通过两步骤方法提高LLM生成数据库查询的稳定性：第一步生成标准化JSON查询条件，第二步执行实际数据库查询。

## 架构

### 系统架构图

```mermaid
graph TD
    A[用户自然语言查询] --> B[ReAct Agent]
    B --> C[结构化查询生成工具]
    C --> D[JSON Schema验证]
    D --> E[查询解析器]
    E --> F[Django ORM查询构建器]
    F --> G[数据库执行]
    G --> H[结果格式化]
    H --> I[LLM最终回答生成]
    I --> J[用户获得回答]
    
    K[JSON Schema定义] --> C
    L[模型内省工具] --> K
    M[航班数据模型] --> F
```

### 两步骤工作流程

1. **第一步：结构化查询生成**
   - LLM接收用户自然语言查询
   - 系统提供JSON Schema规范
   - LLM生成符合Schema的JSON查询条件
   - 系统验证JSON格式正确性

2. **第二步：查询执行**
   - 查询解析器解析JSON条件
   - 查询构建器生成Django ORM queryset
   - 执行数据库查询
   - 格式化结果返回给LLM

## 组件和接口

### 1. 结构化查询生成工具 (StructuredQueryGenerator)

**职责：** 协调LLM生成标准化JSON查询条件

**接口：**
```python
@tool
def generate_structured_flight_query(user_question: str) -> str:
    """
    根据用户自然语言问题生成结构化航班查询条件
    
    Args:
        user_question: 用户的自然语言查询问题
        
    Returns:
        str: JSON格式的查询条件或错误信息
    """
```

**核心功能：**
- 向LLM提供JSON Schema规范
- 引导LLM生成符合格式的查询条件
- 验证生成的JSON格式
- 处理格式错误和重试机制

### 2. 查询解析器 (QueryParser)

**职责：** 解析JSON查询条件并转换为Django ORM查询

**接口：**
```python
class QueryParser:
    def parse_json_query(self, json_query: dict) -> QuerySet:
        """解析JSON查询条件为Django QuerySet"""
        
    def validate_query_conditions(self, conditions: dict) -> bool:
        """验证查询条件的有效性"""
        
    def build_filter_conditions(self, conditions: dict) -> Q:
        """构建Django Q对象过滤条件"""
```

### 3. 查询执行器 (QueryExecutor)

**职责：** 执行数据库查询并格式化结果

**接口：**
```python
@tool
def execute_flight_query(json_query: str) -> str:
    """
    执行结构化航班查询
    
    Args:
        json_query: JSON格式的查询条件
        
    Returns:
        str: 格式化的查询结果
    """
```

### 4. JSON Schema定义器 (SchemaDefinition)

**职责：** 定义和维护查询条件的JSON Schema

**核心Schema结构：**
```json
{
  "type": "object",
  "properties": {
    "model": {
      "type": "string",
      "enum": ["aviation_flight", "aviation_airports", "aviation_aircraft"],
      "description": "要查询的数据模型"
    },
    "conditions": {
      "type": "object",
      "description": "查询条件"
    },
    "joins": {
      "type": "array",
      "description": "关联查询的模型"
    },
    "ordering": {
      "type": "array",
      "description": "排序字段"
    },
    "limit": {
      "type": "integer",
      "description": "结果数量限制"
    }
  }
}
```

## 数据模型

### 支持的查询模型

1. **aviation_flight (航班表)**
   - 主要字段：flight_id, flight_number_icao, airline_name, status
   - 时间字段：departure_scheduled_time, arrival_actual_time
   - 关联字段：aircraft, from_airport, to_airport

2. **aviation_airports (机场表)**
   - 主要字段：icao_code, iata_code, name, country
   - 地理字段：geom_point, timezone
   - 数组字段：to_domestic_airports, from_international_airports

3. **aviation_aircraft (飞机表)**
   - 主要字段：registration, model_name, airline_name
   - 属性字段：age, manufacturer, as_cargo, military

### 查询条件类型

1. **精确匹配 (exact)**
   ```json
   {"field": "country", "operator": "exact", "value": "中国"}
   ```

2. **包含匹配 (contains)**
   ```json
   {"field": "name", "operator": "contains", "value": "首都"}
   ```

3. **范围查询 (range)**
   ```json
   {"field": "departure_scheduled_time", "operator": "range", "value": ["2024-01-01", "2024-12-31"]}
   ```

4. **空值检查 (isnull)**
   ```json
   {"field": "arrival_actual_time", "operator": "isnull", "value": false}
   ```

5. **数组包含 (array_contains)**
   ```json
   {"field": "to_domestic_airports", "operator": "array_contains", "value": "ZBAA"}
   ```

## 错误处理

### 错误类型和处理策略

1. **JSON格式错误**
   - 检测：JSON解析异常
   - 处理：返回格式错误提示，要求重新生成

2. **Schema验证错误**
   - 检测：jsonschema验证失败
   - 处理：返回具体的字段错误信息

3. **数据库查询错误**
   - 检测：Django ORM异常
   - 处理：返回查询错误说明和建议

4. **字段不存在错误**
   - 检测：模型字段验证
   - 处理：返回可用字段列表

### 错误响应格式

```json
{
  "success": false,
  "error_type": "validation_error",
  "error_message": "字段 'invalid_field' 在模型 'aviation_flight' 中不存在",
  "suggestions": ["flight_number_icao", "airline_name", "status"],
  "retry_prompt": "请使用有效的字段名重新生成查询条件"
}
```

## 测试策略

### 单元测试

1. **JSON Schema验证测试**
   - 测试有效和无效的JSON格式
   - 测试各种查询条件类型
   - 测试边界条件和异常情况

2. **查询解析器测试**
   - 测试各种查询条件的解析
   - 测试复杂的逻辑组合
   - 测试关联查询的构建

3. **查询执行器测试**
   - 测试数据库查询的正确性
   - 测试结果格式化
   - 测试性能和分页

### 集成测试

1. **端到端工作流测试**
   - 测试完整的两步骤查询流程
   - 测试与ReAct工作流的集成
   - 测试错误处理和恢复机制

2. **性能测试**
   - 测试查询响应时间
   - 测试大数据量查询的处理
   - 测试并发查询的稳定性

### 测试数据

使用现有的sample_data.py工具生成测试数据：
- 航班数据：包含不同状态、时间范围的航班
- 机场数据：包含国内外不同类型机场
- 飞机数据：包含不同机型、航空公司的飞机

## 实现细节

### 文件结构

```
flights_workflow/
├── tools/
│   ├── structured_query_generator.py  # 结构化查询生成工具
│   ├── query_parser.py               # 查询解析器
│   ├── query_executor.py             # 查询执行器
│   └── flight_query_schema.py        # JSON Schema定义
├── graph.py                          # 更新后的ReAct工作流
└── tests/
    ├── test_structured_query.py      # 结构化查询测试
    ├── test_query_parser.py          # 查询解析测试
    └── test_flight_query_integration.py  # 集成测试
```

### 关键技术选择

1. **JSON Schema验证**：使用jsonschema库进行格式验证
2. **Django ORM**：利用现有的模型定义和查询能力
3. **LangGraph集成**：作为新的工具节点集成到现有工作流
4. **错误处理**：采用结构化错误响应和重试机制
5. **性能优化**：实施查询缓存和结果分页

### 扩展性考虑

1. **模型扩展**：支持添加新的数据模型和字段
2. **查询操作扩展**：支持添加新的查询操作类型
3. **输出格式扩展**：支持不同的结果输出格式
4. **多语言支持**：支持中英文查询和结果显示